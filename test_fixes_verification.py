#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复效果的测试
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens

def test_apostrophe_fixes():
    """测试撇号组合词修复效果"""
    print("=== 验证撇号组合词修复效果 ===")
    
    test_cases = [
        {
            "input": "The film ' s action scenes were intense.",
            "description": "图片中的film ' s问题",
            "expected_complete": "film's"
        },
        {
            "input": "Newton's third law states that every action has an equal and opposite reaction.",
            "description": "Newton's长句",
            "expected_complete": "Newton's"
        },
        {
            "input": "The revolver's double-action trigger allows both cocking and firing in one motion.",
            "description": "revolver's长句",
            "expected_complete": "revolver's"
        },
        {
            "input": "The microscope ' s high power lens reveals tiny details.",
            "description": "microscope ' s问题",
            "expected_complete": "microscope's"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 撇号测试 {i}: {test_case['description']} ---")
        print(f"输入: '{test_case['input']}'")
        
        tokens = extract_tokens(test_case['input'])
        print(f"分词结果: {tokens}")
        print(f"Token数量: {len(tokens)}")
        
        # 检查撇号组合词是否完整
        if test_case['expected_complete'] in tokens:
            print(f"✅ 撇号组合词完整: '{test_case['expected_complete']}'")
        else:
            print(f"❌ 撇号组合词不完整: '{test_case['expected_complete']}'")
            
            # 检查是否被分割
            base_word = test_case['expected_complete'].split("'")[0]
            if base_word in tokens and "'" in tokens and "s" in tokens:
                print(f"❌ 被错误分割为: ['{base_word}', \"'\", 's']")
        
        # 检查长句情况
        if len(tokens) > 10:
            print(f"⚠️  长句包含{len(tokens)}个token，将被分行处理")
        else:
            print(f"✅ 短句包含{len(tokens)}个token，正常处理")

def test_long_sentence_handling():
    """测试长句处理逻辑"""
    print("\n\n=== 验证长句处理逻辑 ===")
    
    # 模拟长句分行逻辑
    def simulate_long_sentence_processing(tokens, max_tokens_per_line=10):
        """模拟长句分行处理"""
        if len(tokens) <= max_tokens_per_line:
            return [tokens]  # 单行
        else:
            # 分割为多行
            chunks = []
            for i in range(0, len(tokens), max_tokens_per_line):
                chunk = tokens[i:i + max_tokens_per_line]
                chunks.append(chunk)
            return chunks
    
    test_sentences = [
        "Newton's third law states that every action has an equal and opposite reaction.",
        "The revolver's double-action trigger allows both cocking and firing in one motion.",
        "The hydroelectric plant generates enough power for the entire city.",
        "Calculate 5² using the power rule.",  # 短句
        "The film's action scenes were intense."  # 短句
    ]
    
    for i, sentence in enumerate(test_sentences, 1):
        print(f"\n--- 长句测试 {i} ---")
        print(f"输入: '{sentence}'")
        
        tokens = extract_tokens(sentence)
        print(f"分词结果: {tokens}")
        print(f"Token数量: {len(tokens)}")
        
        # 模拟分行处理
        chunks = simulate_long_sentence_processing(tokens)
        print(f"分行结果: {len(chunks)}行")
        
        for j, chunk in enumerate(chunks, 1):
            print(f"  第{j}行: {chunk} ({len(chunk)}个token)")
        
        # 检查撇号组合词是否保持完整
        apostrophe_words = [token for token in tokens if "'" in token and len(token) > 2]
        if apostrophe_words:
            print(f"✅ 撇号组合词保持完整: {apostrophe_words}")
        
        if len(chunks) > 1:
            print("✅ 长句将被分行显示，提高可读性")
        else:
            print("✅ 短句正常显示")

def test_table_layout_simulation():
    """模拟表格布局效果"""
    print("\n\n=== 模拟表格布局效果 ===")
    
    test_case = "Newton's third law states that every action has an equal and opposite reaction."
    print(f"测试句子: '{test_case}'")
    
    tokens = extract_tokens(test_case)
    print(f"分词结果: {tokens}")
    print(f"Token数量: {len(tokens)}")
    
    max_tokens_per_line = 10
    
    print(f"\n使用每行最大{max_tokens_per_line}个token的布局:")
    
    if len(tokens) > max_tokens_per_line:
        chunks = []
        for i in range(0, len(tokens), max_tokens_per_line):
            chunk = tokens[i:i + max_tokens_per_line]
            chunks.append(chunk)
        
        print("表格布局预览:")
        for i, chunk in enumerate(chunks, 1):
            print(f"\n表格 {i}:")
            print("+" + "-" * (len(chunk) * 12 - 1) + "+")
            
            # 英文行
            english_row = "|"
            for token in chunk:
                english_row += f" {token:^10} |"
            print(english_row)
            
            print("+" + "-" * (len(chunk) * 12 - 1) + "+")
            
            # 中文行（模拟）
            chinese_row = "|"
            for token in chunk:
                if "'" in token:
                    chinese_row += f" {'(撇号词)':^10} |"
                elif token.isalpha():
                    chinese_row += f" {'(释义)':^10} |"
                else:
                    chinese_row += f" {'':^10} |"
            print(chinese_row)
            
            print("+" + "-" * (len(chunk) * 12 - 1) + "+")
    else:
        print("短句，单表格显示")

def test_specific_issues():
    """测试图片中的具体问题"""
    print("\n\n=== 测试图片中的具体问题 ===")
    
    # 图片中显示的问题句子
    problem_sentences = [
        "The film ' s action scenes were intense.",
        "Newton's third law states that every action has an equal and opposite reaction.",
        "The revolver's double-action trigger allows both cocking and firing in one motion."
    ]
    
    for i, sentence in enumerate(problem_sentences, 1):
        print(f"\n--- 问题句子 {i} ---")
        print(f"输入: '{sentence}'")
        
        tokens = extract_tokens(sentence)
        print(f"分词结果: {tokens}")
        
        # 检查撇号组合词
        apostrophe_issues = []
        if "film" in tokens and "'" in tokens and "s" in tokens:
            apostrophe_issues.append("film's被分割")
        elif "film's" in tokens:
            print("✅ film's正确合并")
            
        if "Newton" in tokens and "'" in tokens and "s" in tokens:
            apostrophe_issues.append("Newton's被分割")
        elif "Newton's" in tokens:
            print("✅ Newton's正确合并")
            
        if "revolver" in tokens and "'" in tokens and "s" in tokens:
            apostrophe_issues.append("revolver's被分割")
        elif "revolver's" in tokens:
            print("✅ revolver's正确合并")
        
        if apostrophe_issues:
            print(f"❌ 撇号问题: {', '.join(apostrophe_issues)}")
        
        # 检查长句处理
        if len(tokens) > 10:
            print(f"✅ 长句({len(tokens)}个token)将被分行处理，改善显示效果")
        else:
            print(f"✅ 短句({len(tokens)}个token)正常显示")

if __name__ == "__main__":
    test_apostrophe_fixes()
    test_long_sentence_handling()
    test_table_layout_simulation()
    test_specific_issues()
    print("\n=== 修复效果验证完成 ===")
