#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复的功能
"""

import re
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens

def test_special_character_spacing():
    """测试特殊字符间空格问题的修复"""
    print("=== 测试特殊字符间空格问题修复 ===")
    
    test_cases = [
        # 测试数字+x的情况
        ("Calculate 5² using the power rule.", "Calculate 5² using the power rule."),
        ("The microscope has a magnification power of 1000 x.", "The microscope has a magnification power of 1000x."),
        
        # 测试单词+'s的情况
        ("The film ' s action scenes were intense.", "The film's action scenes were intense."),
        ("<PERSON> ' s third law states that every action has an equal and opposite reaction.", "Newton's third law states that every action has an equal and opposite reaction."),
        
        # 测试其他撇号情况
        ("Therevolver ' s double-action trigger allows both cocking and firing in one motion.", "Therevolver's double-action trigger allows both cocking and firing in one motion."),
        
        # 测试km/h的情况
        ("The speed is 60 km / h.", "The speed is 60 km/h."),
    ]
    
    for i, (input_text, expected_output) in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入: {input_text}")
        
        # 应用extract_tokens函数中的预处理逻辑
        processed_text = input_text
        
        # 预处理：去掉km和h之间的多余空格
        processed_text = re.sub(r'km\s+/\s+h', 'km/h', processed_text)
        
        # 处理特殊字符间的空格问题
        # 处理数字+x的情况，如"1000 x" -> "1000x"
        processed_text = re.sub(r'(\d+)\s+([xX])', r'\1\2', processed_text)
        # 处理单词+'s的情况，如"film ' s" -> "film's"
        processed_text = re.sub(r"(\w+)\s+'\s*s\b", r"\1's", processed_text)
        # 处理单词+'的情况，如"Newton ' s" -> "Newton's"
        processed_text = re.sub(r"(\w+)\s+'\s*([a-zA-Z])", r"\1'\2", processed_text)
        # 处理其他撇号情况
        processed_text = re.sub(r"(\w+)\s+'\s+", r"\1' ", processed_text)
        
        print(f"处理后: {processed_text}")
        print(f"期望: {expected_output}")
        
        if processed_text == expected_output:
            print("✅ 通过")
        else:
            print("❌ 失败")
        
        # 测试extract_tokens函数
        tokens = extract_tokens(input_text)
        print(f"分词结果: {tokens}")

def test_page_size_copy():
    """测试纸张大小复制功能"""
    print("\n\n=== 测试纸张大小复制功能 ===")
    
    try:
        from docx import Document
        from docx.shared import Cm
        
        # 创建一个测试文档，设置B5纸张大小
        doc_test = Document()
        section = doc_test.sections[0]
        
        # B5纸张大小：182mm x 257mm
        section.page_width = Cm(18.2)
        section.page_height = Cm(25.7)
        
        # 保存测试文档
        test_doc_path = "test_b5_document.docx"
        doc_test.save(test_doc_path)
        
        print(f"✅ 创建了测试B5文档: {test_doc_path}")
        print(f"   页面宽度: {section.page_width.cm:.1f}cm")
        print(f"   页面高度: {section.page_height.cm:.1f}cm")
        
        # 测试读取文档
        doc_read = Document(test_doc_path)
        section_read = doc_read.sections[0]
        
        print(f"✅ 读取文档页面设置:")
        print(f"   页面宽度: {section_read.page_width.cm:.1f}cm")
        print(f"   页面高度: {section_read.page_height.cm:.1f}cm")
        
        # 清理测试文件
        if os.path.exists(test_doc_path):
            os.remove(test_doc_path)
            print(f"✅ 清理测试文件: {test_doc_path}")
            
    except ImportError:
        print("❌ 无法导入docx模块，跳过纸张大小测试")
    except Exception as e:
        print(f"❌ 测试纸张大小功能时出错: {e}")

if __name__ == "__main__":
    test_special_character_spacing()
    test_page_size_copy()
    print("\n=== 测试完成 ===")
