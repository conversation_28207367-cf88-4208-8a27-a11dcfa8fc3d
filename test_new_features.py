#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增功能
"""

import os
import json
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_file_operations():
    """测试配置文件操作"""
    print("=== 测试配置文件操作 ===")
    
    config_file = "test_app_config.json"
    test_api_key = "sk-test123456789"
    
    try:
        # 测试保存配置
        config = {
            'api_key': test_api_key
        }
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 成功创建测试配置文件: {config_file}")
        
        # 测试加载配置
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                loaded_config = json.load(f)
                loaded_api_key = loaded_config.get('api_key', '')
                
            if loaded_api_key == test_api_key:
                print(f"✅ 成功加载API Key: {loaded_api_key}")
            else:
                print(f"❌ API Key加载失败，期望: {test_api_key}, 实际: {loaded_api_key}")
        
        # 清理测试文件
        if os.path.exists(config_file):
            os.remove(config_file)
            print(f"✅ 清理测试文件: {config_file}")
            
    except Exception as e:
        print(f"❌ 配置文件操作测试失败: {e}")

def test_button_order():
    """测试按钮顺序（通过代码检查）"""
    print("\n=== 测试按钮顺序 ===")
    
    try:
        # 读取源代码文件
        with open('oringin.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找按钮创建的代码行
        lines = content.split('\n')
        button_lines = []
        
        for i, line in enumerate(lines):
            if 'ttk.Button(button_frame' in line and ('处理文档A' in line or '帮助' in line or '开始处理' in line):
                button_lines.append((i+1, line.strip()))
        
        print("找到的按钮创建代码:")
        for line_num, line_content in button_lines:
            print(f"  第{line_num}行: {line_content}")
        
        # 检查按钮顺序
        if len(button_lines) >= 3:
            # 检查第一个按钮是否是"处理文档A"
            if '处理文档A' in button_lines[0][1] and 'column=0' in button_lines[0][1]:
                print("✅ 第一个按钮是'处理文档A'")
            else:
                print("❌ 第一个按钮不是'处理文档A'")
            
            # 检查第二个按钮是否是"帮助"
            if '帮助' in button_lines[1][1] and 'column=1' in button_lines[1][1]:
                print("✅ 第二个按钮是'帮助'")
            else:
                print("❌ 第二个按钮不是'帮助'")
            
            # 检查第三个按钮是否是"开始处理"
            if '开始处理' in button_lines[2][1] and 'column=2' in button_lines[2][1]:
                print("✅ 第三个按钮是'开始处理'")
            else:
                print("❌ 第三个按钮不是'开始处理'")
        else:
            print("❌ 未找到足够的按钮定义")
            
    except Exception as e:
        print(f"❌ 按钮顺序测试失败: {e}")

def test_config_methods():
    """测试配置方法是否存在"""
    print("\n=== 测试配置方法 ===")
    
    try:
        # 读取源代码文件
        with open('oringin.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查必要的方法是否存在
        required_methods = [
            'def load_config(self):',
            'def save_config(self):',
            'def on_closing(self):'
        ]
        
        for method in required_methods:
            if method in content:
                print(f"✅ 找到方法: {method}")
            else:
                print(f"❌ 未找到方法: {method}")
        
        # 检查是否有配置文件相关的代码
        if 'self.config_file = "app_config.json"' in content:
            print("✅ 找到配置文件定义")
        else:
            print("❌ 未找到配置文件定义")
        
        # 检查是否有加载配置的调用
        if 'self.load_config()' in content:
            print("✅ 找到加载配置的调用")
        else:
            print("❌ 未找到加载配置的调用")
        
        # 检查是否有窗口关闭事件绑定
        if 'self.root.protocol("WM_DELETE_WINDOW", self.on_closing)' in content:
            print("✅ 找到窗口关闭事件绑定")
        else:
            print("❌ 未找到窗口关闭事件绑定")
            
    except Exception as e:
        print(f"❌ 配置方法测试失败: {e}")

def test_auto_path_update():
    """测试自动路径更新功能"""
    print("\n=== 测试自动路径更新功能 ===")
    
    try:
        # 读取源代码文件
        with open('oringin.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查process_document_A方法中是否有路径更新代码
        if 'self.word_path.delete(0, tk.END)' in content and 'self.word_path.insert(0, output_path)' in content:
            print("✅ 找到自动路径更新代码")
        else:
            print("❌ 未找到自动路径更新代码")
        
        # 检查是否在正确的位置（process_document_A方法中）
        lines = content.split('\n')
        in_process_document_a = False
        found_path_update = False
        
        for line in lines:
            if 'def process_document_A(self):' in line:
                in_process_document_a = True
            elif 'def ' in line and in_process_document_a:
                # 进入下一个方法，退出当前方法
                break
            elif in_process_document_a and 'self.word_path.delete(0, tk.END)' in line:
                found_path_update = True
                break
        
        if found_path_update:
            print("✅ 路径更新代码在正确的方法中")
        else:
            print("❌ 路径更新代码不在process_document_A方法中")
            
    except Exception as e:
        print(f"❌ 自动路径更新测试失败: {e}")

if __name__ == "__main__":
    test_config_file_operations()
    test_button_order()
    test_config_methods()
    test_auto_path_update()
    print("\n=== 测试完成 ===")
