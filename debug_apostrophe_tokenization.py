#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试撇号组合词分词问题
"""

import sys
import os
import re

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens

def debug_tokenization_process(text):
    """逐步调试分词过程"""
    print(f"=== 调试分词过程：'{text}' ===")
    
    # 步骤1：原始文本
    print(f"1. 原始文本: '{text}'")
    
    # 步骤2：模拟预处理阶段
    print("\n2. 预处理阶段:")
    
    # 2.1 处理数字+特定符号的情况
    processed_text = re.sub(r'(\d+)\s+([xyzXYZ²³¹⁰⁴⁵⁶⁷⁸⁹%π°])', r'\1\2', text)
    print(f"   数字+符号处理后: '{processed_text}'")
    
    # 2.2 撇号处理
    apostrophes = r"['\'`''']"
    
    # 处理单词+'s的情况
    processed_text = re.sub(rf"(\w+)\s*{apostrophes}\s*s\b", r"\1's", processed_text)
    print(f"   撇号+'s处理后: '{processed_text}'")
    
    # 处理单词+'t的情况
    processed_text = re.sub(rf"(\w+)\s*{apostrophes}\s*t\b", r"\1't", processed_text)
    print(f"   撇号+'t处理后: '{processed_text}'")
    
    # 处理单词+'re的情况
    processed_text = re.sub(rf"(\w+)\s*{apostrophes}\s*re\b", r"\1're", processed_text)
    print(f"   撇号+'re处理后: '{processed_text}'")
    
    # 处理单词+'ll的情况
    processed_text = re.sub(rf"(\w+)\s*{apostrophes}\s*ll\b", r"\1'll", processed_text)
    print(f"   撇号+'ll处理后: '{processed_text}'")
    
    # 处理单词+'ve的情况
    processed_text = re.sub(rf"(\w+)\s*{apostrophes}\s*ve\b", r"\1've", processed_text)
    print(f"   撇号+'ve处理后: '{processed_text}'")
    
    # 处理单词+'d的情况
    processed_text = re.sub(rf"(\w+)\s*{apostrophes}\s*d\b", r"\1'd", processed_text)
    print(f"   撇号+'d处理后: '{processed_text}'")
    
    # 处理单词+'m的情况
    processed_text = re.sub(rf"(\w+)\s*{apostrophes}\s*m\b", r"\1'm", processed_text)
    print(f"   撇号+'m处理后: '{processed_text}'")
    
    # 处理其他撇号+字母的情况
    processed_text = re.sub(rf"(\w+)\s*{apostrophes}\s*([a-zA-Z]+)", r"\1'\2", processed_text)
    print(f"   其他撇号+字母处理后: '{processed_text}'")
    
    # 处理孤立的撇号
    processed_text = re.sub(rf"(\w+)\s*{apostrophes}\s+", r"\1' ", processed_text)
    print(f"   孤立撇号处理后: '{processed_text}'")
    
    # 步骤3：基本分词模式
    print("\n3. 基本分词阶段:")
    basic_pattern = r"""
        [A-Za-z]+['\'`'''][a-zA-Z]+ |     # 撇号组合词（支持所有撇号字符）
        [A-Za-z]+ |                       # 普通单词
        \d+(?:\.\d+)? |                   # 数字（包括小数）
        [^\w\s]                           # 标点符号和其他字符
    """
    basic_tokens = re.findall(basic_pattern, processed_text, re.VERBOSE)
    print(f"   基本分词结果: {basic_tokens}")
    
    # 检查撇号组合词是否被正确识别
    apostrophe_words = [token for token in basic_tokens if "'" in token and len(token) > 2]
    print(f"   识别到的撇号组合词: {apostrophe_words}")
    
    # 步骤4：智能合并阶段（模拟）
    print("\n4. 智能合并阶段:")
    print("   （这个阶段主要处理数字与符号的合并，对撇号组合词影响较小）")
    
    # 步骤5：实际的extract_tokens结果
    print("\n5. 实际extract_tokens结果:")
    actual_tokens = extract_tokens(text)
    print(f"   实际结果: {actual_tokens}")
    
    # 步骤6：问题分析
    print("\n6. 问题分析:")
    expected_apostrophe_words = []
    if "film's" in text or "film ' s" in text:
        expected_apostrophe_words.append("film's")
    if "Newton's" in text or "Newton ' s" in text:
        expected_apostrophe_words.append("Newton's")
    
    for expected_word in expected_apostrophe_words:
        if expected_word in actual_tokens:
            print(f"   ✅ '{expected_word}' 被正确识别为单个token")
        else:
            print(f"   ❌ '{expected_word}' 未被正确识别为单个token")
            
            # 检查是否被分割
            base_word = expected_word.split("'")[0]
            if base_word in actual_tokens:
                print(f"      发现基础词 '{base_word}' 在结果中")
                if "'" in actual_tokens:
                    print(f"      发现撇号 \"'\" 被单独分离")
                if "s" in actual_tokens:
                    print(f"      发现 's' 被单独分离")

def test_specific_patterns():
    """测试特定的撇号模式"""
    print("\n\n=== 测试特定撇号模式 ===")
    
    patterns_to_test = [
        r"(\w+)\s*['\'`''']\s*s\b",  # 单词+'s
        r"(\w+)\s*['\'`''']\s*([a-zA-Z]+)",  # 单词+撇号+字母
    ]
    
    test_texts = [
        "film's",
        "film 's", 
        "film ' s",
        "Newton's",
        "Newton 's",
        "Newton ' s"
    ]
    
    for pattern in patterns_to_test:
        print(f"\n测试模式: {pattern}")
        for text in test_texts:
            match = re.search(pattern, text)
            if match:
                print(f"   '{text}' -> 匹配: {match.groups()}")
            else:
                print(f"   '{text}' -> 无匹配")

def test_basic_pattern_directly():
    """直接测试基本分词模式"""
    print("\n\n=== 直接测试基本分词模式 ===")
    
    basic_pattern = r"""
        [A-Za-z]+['\'`'''][a-zA-Z]+ |     # 撇号组合词
        [A-Za-z]+ |                       # 普通单词
        \d+(?:\.\d+)? |                   # 数字
        [^\w\s]                           # 标点符号
    """
    
    test_texts = [
        "film's action",
        "Newton's law",
        "The film's action scenes were intense.",
        "Newton's third law states that every action has an equal and opposite reaction."
    ]
    
    for text in test_texts:
        print(f"\n测试文本: '{text}'")
        tokens = re.findall(basic_pattern, text, re.VERBOSE)
        print(f"分词结果: {tokens}")
        
        apostrophe_words = [token for token in tokens if "'" in token and len(token) > 2]
        if apostrophe_words:
            print(f"撇号组合词: {apostrophe_words}")
        else:
            print("❌ 未识别到撇号组合词")

if __name__ == "__main__":
    # 测试问题句子
    test_sentences = [
        "The film's action scenes were intense.",
        "Newton's third law states that every action has an equal and opposite reaction."
    ]
    
    for sentence in test_sentences:
        debug_tokenization_process(sentence)
        print("\n" + "="*80 + "\n")
    
    test_specific_patterns()
    test_basic_pattern_directly()
    
    print("\n=== 调试完成 ===")
