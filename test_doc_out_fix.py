#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试doc_out修复
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入是否正常"""
    try:
        from oringin import WordAssistantApp, extract_tokens
        print("✅ 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_extract_tokens():
    """测试分词功能"""
    try:
        from oringin import extract_tokens
        
        test_cases = [
            "The film's action scenes were intense.",
            "Newton's third law states that every action has an equal and opposite reaction."
        ]
        
        for case in test_cases:
            tokens = extract_tokens(case)
            print(f"✅ 分词测试通过: '{case}' -> {len(tokens)} tokens")
            
            # 检查撇号组合词
            apostrophe_words = [token for token in tokens if "'" in token and len(token) > 2]
            if apostrophe_words:
                print(f"   撇号组合词: {apostrophe_words}")
        
        return True
    except Exception as e:
        print(f"❌ 分词测试失败: {e}")
        return False

def test_class_creation():
    """测试类创建"""
    try:
        import tkinter as tk
        from oringin import WordAssistantApp
        
        # 创建一个临时的root窗口（不显示）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        app = WordAssistantApp(root)
        print("✅ 类创建成功")
        
        root.destroy()
        return True
    except Exception as e:
        print(f"❌ 类创建失败: {e}")
        return False

if __name__ == "__main__":
    print("=== 测试doc_out修复效果 ===")
    
    success = True
    
    print("\n1. 测试导入...")
    success &= test_import()
    
    print("\n2. 测试分词功能...")
    success &= test_extract_tokens()
    
    print("\n3. 测试类创建...")
    success &= test_class_creation()
    
    print(f"\n=== 测试结果: {'✅ 全部通过' if success else '❌ 存在问题'} ===")
