import tkinter as tk
from tkinter import filedialog, messagebox
from tkinter import ttk
from openpyxl import load_workbook
from docx import Document
from docx.shared import Pt, Cm
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.enum.text import WD_LINE_SPACING
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import dashscope
from dashscope import Generation
import os
import logging
import json

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', filename='translation.log', filemode='w')

def strip_punct(word):
    return re.sub(r'[\s\.,!?;:，。!“”‘’\(\)\'"—…；：？！\[\]{}]+$', '', word).lower()

def split_meaning_two_lines(meaning, max_len=8):
    seps = ['，', ',', '。', '；', ';', '、', ' ']
    for s in seps:
        if s in meaning and len(meaning) > max_len:
            idx = meaning.find(s)
            if idx > 0 and idx < len(meaning)-1:
                return meaning[:idx+1] + '\n' + meaning[idx+1:]
    if len(meaning) > max_len:
        mid = len(meaning) // 2
        return meaning[:mid] + '\n' + meaning[mid:]
    return meaning

def set_table_noborder_and_tight(table, colwidths):
    tbl = table._tbl
    tblPr = tbl.xpath('./w:tblPr')[0]
    tblBorders = OxmlElement('w:tblBorders')
    for border in ['top', 'left', 'bottom', 'right', 'insideH', 'insideV']:
        border_elem = OxmlElement(f'w:{border}')
        border_elem.set(qn('w:val'), 'none')
        tblBorders.append(border_elem)
    tblPr.append(tblBorders)
    for cell in table._cells:
        tcp = cell._tc.get_or_add_tcPr()
        tcMar = OxmlElement('w:tcMar')
        for m in ('top', 'left', 'bottom', 'right'):
            node = OxmlElement('w:' + m)
            node.set(qn('w:w'), "0")
            node.set(qn('w:type'), "dxa")
            tcMar.append(node)
        tcp.append(tcMar)
    for row in table.rows:
        for idx, cell in enumerate(row.cells):
            if idx < len(colwidths):
                cell.width = colwidths[idx]

def load_wordlist(excel_path):
    wb = load_workbook(excel_path)
    ws = wb.active
    word_dict = {}
    word_set = set()
    header = [str(i).strip() for i in next(ws.iter_rows(values_only=True))]
    word_idx = 0
    mean_idx = 1
    if "单词" in header:
        word_idx = header.index("单词")
    if "释义" in header:
        mean_idx = header.index("释义")
    for row in ws.iter_rows(min_row=2, values_only=True):
        if row and row[word_idx]:
            w = str(row[word_idx]).strip()
            w_norm = re.sub(r'[^A-Za-z0-9\'\-]', '', w).lower()
            word_set.add(w_norm)
            mean = str(row[mean_idx]).strip() if len(row) > mean_idx and row[mean_idx] else ""
            word_dict[w_norm] = mean
    return word_set, word_dict

def extract_tokens(text):
    # 预处理：去掉km和h之间的多余空格
    text = re.sub(r'km\s+/\s+h', 'km/h', text)

    # 处理特殊字符间的空格问题
    # 处理数字+x的情况，如"1000 x" -> "1000x"
    text = re.sub(r'(\d+)\s+([xX])', r'\1\2', text)
    # 处理单词+'s的情况，如"film ' s" -> "film's"
    text = re.sub(r"(\w+)\s+'\s*s\b", r"\1's", text)
    # 处理单词+'的情况，如"Newton ' s" -> "Newton's"
    text = re.sub(r"(\w+)\s+'\s*([a-zA-Z])", r"\1'\2", text)
    # 处理其他撇号情况
    text = re.sub(r"(\w+)\s+'\s+", r"\1' ", text)

    # 改进的分词模式，更好地处理语义关联的字符序列
    # 使用更精确的正则表达式来匹配各种模式

    # 首先用简单的模式分割，然后进行智能合并
    # 基本分词：单词、数字、标点符号
    basic_pattern = r"[A-Za-z]+(?:[''][a-zA-Z]+)*|\d+(?:\.\d+)?|[^\w\s]|\S"
    tokens = re.findall(basic_pattern, text)

    # 智能合并：将语义相关的token合并
    result = []
    i = 0
    while i < len(tokens):
        if i >= len(tokens):
            break

        current_token = tokens[i]
        merged_token = current_token
        j = i + 1

        # 持续检查后续token是否应该合并
        while j < len(tokens):
            next_token = tokens[j]
            should_merge = False

            # 1. 货币符号与数字合并（$, €, £, ¥ + 数字）
            if (re.match(r'^[$€£¥]$', merged_token) and
                re.match(r'^\d+', next_token)):
                should_merge = True

            # 2. 数字与字母/符号合并（数字 + x, ², π等）
            elif (re.match(r'^\d+(\.\d+)?$', merged_token) and
                  re.match(r'^[a-zA-Z²³¹⁰⁴⁵⁶⁷⁸⁹xX%π°/]', next_token)):
                should_merge = True

            # 2a. 数字+字母组合继续与标点合并（如 1000x + .）
            elif (re.match(r'^\d+(\.\d+)?[a-zA-Z²³¹⁰⁴⁵⁶⁷⁸⁹xX%π°/]+$', merged_token) and
                  re.match(r'^[,\.!?;:\"\"''…—）\)]$', next_token)):
                should_merge = True

            # 3. 数字与单位合并（如 60 + km, km + / + h）
            elif (re.match(r'^\d+$', merged_token) and next_token == 'km'):
                should_merge = True
            elif (merged_token == 'km' and next_token == '/'):
                should_merge = True
            elif (merged_token == 'km/' and next_token == 'h'):
                should_merge = True
            elif (re.match(r'^\d+km$', merged_token) and next_token == '/'):
                should_merge = True
            elif (re.match(r'^\d+km/$', merged_token) and next_token == 'h'):
                should_merge = True

            # 4. 字母与上标/下标合并
            elif (re.match(r'^[a-zA-Z]+$', merged_token) and
                  re.match(r'^[²³¹⁰⁴⁵⁶⁷⁸⁹]', next_token)):
                should_merge = True

            # 5. 单词与标点符号合并（word + .!?等）
            elif (re.match(r'^[a-zA-Z0-9]+$', merged_token) and
                  re.match(r'^[,\.!?;:\"\"''…—）\)]$', next_token)):
                should_merge = True

            # 6. 已经包含标点的单词继续与标点合并（如 word! + "）
            elif (re.match(r'^[a-zA-Z0-9]+[,\.!?;:\"\"''…—）\)]+$', merged_token) and
                  re.match(r'^[,\.!?;:\"\"''…—（）\(\)]$', next_token)):
                should_merge = True

            # 7. 标点符号之间的合并（如 ! + "）
            elif (re.match(r'^[,\.!?;:\"\"''…—（）\(\)]$', merged_token) and
                  re.match(r'^[,\.!?;:\"\"''…—（）\(\)]$', next_token)):
                should_merge = True

            # 7. 数学运算符与数字合并（= + 数字）
            elif (merged_token in ['=', '+', '-', '×', '÷', '/', '*'] and
                  re.match(r'^[\d\w]', next_token)):
                should_merge = True

            # 7a. 数学表达式继续合并（如 =3.14 + π）
            elif (re.match(r'^[=+\-×÷/*]\d+(\.\d+)?$', merged_token) and
                  re.match(r'^[a-zA-Z²³¹⁰⁴⁵⁶⁷⁸⁹π°]', next_token)):
                should_merge = True

            # 8. 小数点合并（数字 + . + 数字）
            elif (re.match(r'^\d+$', merged_token) and next_token == '.' and
                  j + 1 < len(tokens) and re.match(r'^\d+', tokens[j + 1])):
                # 特殊处理：合并三个token（数字.数字）
                if j + 1 < len(tokens):
                    merged_token = merged_token + next_token + tokens[j + 1]
                    j += 2
                    continue

            if should_merge:
                merged_token += next_token
                j += 1
            else:
                break

        result.append(merged_token)
        i = j

    return result

def split_sentence_for_context(text, idx):
    s, e = 0, len(text)
    for i in range(idx, 0, -1):
        if text[i] in '.!?':
            s = i + 1
            break
    for i in range(idx, len(text)):
        if text[i] in '.!?':
            e = i + 1
            break
    return text[s:e].strip()

def get_chinese_meaning_batch(api_key, tasks, progress_cb=lambda x, y: None):
    dashscope.api_key = api_key
    result = {}
    with ThreadPoolExecutor(max_workers=8) as pool:
        future_to_key = {}
        for (word, sent) in tasks:
            future = pool.submit(get_chinese_meaning, api_key, word, sent)
            future_to_key[future] = (word, sent)
        total = len(future_to_key)
        for idx, f in enumerate(as_completed(future_to_key)):
            key = future_to_key[f]
            try:
                val = f.result()
            except Exception:
                val = ""
            result[key] = val
            percent = int((idx + 1) / total * 100)
            progress_cb(f"AI进度：{idx + 1}/{total}", percent)
    return result

def get_chinese_meaning(api_key, word, sentence):
    prompt = f"请给出英文句子：'{sentence}' 中单词 '{word}' 在该句中的最准确中文意思，不要泛泛释义。直接输出汉字即可。"
    logging.info(f"Requesting translation for '{word}' with prompt: {prompt}")
    messages = [
        {"role": "system", "content": "你是地道的英汉词义专家"},
        {"role": "user", "content": prompt}
    ]
    try:
        response = Generation.call(
            model="qwen-turbo",
            messages=messages,
            result_format="message",
            max_tokens=32
        )
        translation = response.output.choices[0].message.content.strip()
        logging.info(f"API Response for '{word}': {response}")
        logging.info(f"Received translation for '{word}': '{translation}'")
        return translation
    except Exception as e:
        logging.error(f"Error translating '{word}': {e}", exc_info=True)
        return ""

def split_sentence_by_period(text):
    sentences = text.split(".")
    return [sentence.strip() + '.' for sentence in sentences if sentence.strip()]

class WordAssistantApp:
    def __init__(self, root):
        self.root = root
        self.root.title("智能词义文档处理器（自适应单元格宽度）")
        self.config_file = "app_config.json"
        style = ttk.Style()
        style.theme_use('clam')

        file_frame = ttk.LabelFrame(root, text="文件选择", padding=10)
        file_frame.grid(row=0, column=0, padx=10, pady=5, sticky="ew")

        ttk.Label(file_frame, text="Word文档A:").grid(row=0, column=0, sticky="e")
        self.word_path = ttk.Entry(file_frame, width=50)
        self.word_path.grid(row=0, column=1, padx=5)
        ttk.Button(file_frame, text="选择", command=self.select_word).grid(row=0, column=2, padx=5)
        ttk.Label(file_frame, text="选择要处理的Word文档").grid(row=0, column=3, padx=5, sticky="w")

        ttk.Label(file_frame, text="Excel词表B:").grid(row=1, column=0, sticky="e")
        self.excel_path = ttk.Entry(file_frame, width=50)
        self.excel_path.grid(row=1, column=1, padx=5)
        ttk.Button(file_frame, text="选择", command=self.select_excel).grid(row=1, column=2, padx=5)
        ttk.Label(file_frame, text="选择包含词表的Excel文件").grid(row=1, column=3, padx=5, sticky="w")

        settings_frame = ttk.LabelFrame(root, text="设置", padding=10)
        settings_frame.grid(row=1, column=0, padx=10, pady=5, sticky="ew")

        ttk.Label(settings_frame, text="中文字体:").grid(row=0, column=0, sticky="e")
        self.fonttype = tk.StringVar(value="宋体")
        ttk.OptionMenu(settings_frame, self.fonttype, "宋体", "黑体", "楷体", "微软雅黑").grid(row=0, column=1, sticky="w")
        ttk.Label(settings_frame, text="中文释义的字体").grid(row=0, column=2, padx=5, sticky="w")

        ttk.Label(settings_frame, text="中文字号:").grid(row=1, column=0, sticky="e")
        self.fontsize = tk.IntVar(value=10)
        ttk.OptionMenu(settings_frame, self.fontsize, 8, 9, 10, 12, 14).grid(row=1, column=1, sticky="w")
        ttk.Label(settings_frame, text="中文释义的字号").grid(row=1, column=2, padx=5, sticky="w")

        ttk.Label(settings_frame, text="API Key:").grid(row=2, column=0, sticky="e")
        self.apikey = ttk.Entry(settings_frame, width=50)
        self.apikey.grid(row=2, column=1)
        ttk.Label(settings_frame, text="用于AI翻译的API密钥").grid(row=2, column=2, padx=5, sticky="w")

        # 加载保存的API Key
        self.load_config()

        ttk.Label(settings_frame, text="最大单元格宽度(厘米):").grid(row=3, column=0, sticky="e")
        self.max_width_var = tk.DoubleVar(value=2.2)
        ttk.Spinbox(settings_frame, from_=1.0, to=5.0, increment=0.01, width=7, textvariable=self.max_width_var).grid(row=3, column=1, sticky='w')
        ttk.Label(settings_frame, text="表格单元格的最大宽度").grid(row=3, column=2, padx=5, sticky="w")

        self.progress = ttk.Label(root, text="")
        self.progress.grid(row=2, column=0, pady=5)
        self.progressbar = ttk.Progressbar(root, orient="horizontal", length=340, mode="determinate", maximum=100)
        self.progressbar.grid(row=3, column=0, pady=10)

        button_frame = ttk.Frame(root)
        button_frame.grid(row=4, column=0, pady=10)
        ttk.Button(button_frame, text="处理文档A", command=self.process_document_A).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="帮助", command=self.show_help).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="开始处理", command=self.run).grid(row=0, column=2, padx=5)

        # 绑定窗口关闭事件，保存配置
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 加载API Key
                    if 'api_key' in config:
                        self.apikey.delete(0, tk.END)
                        self.apikey.insert(0, config['api_key'])
        except Exception as e:
            print(f"加载配置文件失败: {e}")

    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                'api_key': self.apikey.get().strip()
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def on_closing(self):
        """窗口关闭时的处理"""
        self.save_config()
        self.root.destroy()

    def select_word(self):
        p = filedialog.askopenfilename(filetypes=[("Word文档", "*.docx")])
        if p:
            self.word_path.delete(0, tk.END)
            self.word_path.insert(0, p)

    def select_excel(self):
        p = filedialog.askopenfilename(filetypes=[("Excel", "*.xlsx")])
        if p:
            self.excel_path.delete(0, tk.END)
            self.excel_path.insert(0, p)

    def set_progress(self, txt, percent=None):
        self.progress.config(text=txt)
        if percent is not None:
            self.progressbar['value'] = percent
        self.root.update()

    def run(self):
        wordf = self.word_path.get()
        excelf = self.excel_path.get()
        apikey = self.apikey.get().strip()
        if not wordf:
            messagebox.showerror("错误", "请选择Word文档！")
            return
        if not excelf:
            messagebox.showerror("错误", "请选择Excel词表！")
            return
        if not apikey:
            messagebox.showerror("错误", "请填写API Key！")
            return
        self.set_progress("开始处理", 0)

        def _worker():
            try:
                outpath = self.process_doc(wordf, excelf, self.fonttype.get(), self.fontsize.get(), apikey, self.set_progress)
                self.root.after(0, lambda: messagebox.showinfo("完成", f"处理完成！输出文档为：\n{outpath}"))
            except Exception as e:
                err_msg = str(e)
                self.root.after(0, lambda: messagebox.showerror("错误", f"处理失败：{err_msg}"))
            self.set_progress("", 0)

        threading.Thread(target=_worker, daemon=True).start()

    def show_help(self):
        help_text = (
            "使用说明：\n"
            "1. Word文档A：选择需要处理的Word文档。\n"
            "2. Excel词表B：选择包含单词和释义的Excel文件。\n"
            "3. 中文字体和字号：设置中文释义的字体和大小。\n"
            "4. API Key：输入用于AI翻译的密钥。\n"
            "5. 最大单元格宽度：设置表格单元格的最大宽度（单位：厘米）。\n"
            "6. 点击“开始处理”生成带释义的文档。"
        )
        messagebox.showinfo("帮助", help_text)

    def process_document_A(self):
        wordf = self.word_path.get()
        if not wordf:
            messagebox.showerror("错误", "请选择Word文档！")
            return
        
        doc_in = Document(wordf)
        doc_out = Document()

        # 复制原文档的纸张大小设置
        if doc_in.sections:
            section_in = doc_in.sections[0]
            section_out = doc_out.sections[0]

            # 复制页面尺寸
            section_out.page_width = section_in.page_width
            section_out.page_height = section_in.page_height

            # 复制页边距
            section_out.left_margin = section_in.left_margin
            section_out.right_margin = section_in.right_margin
            section_out.top_margin = section_in.top_margin
            section_out.bottom_margin = section_in.bottom_margin

            # 复制页面方向
            section_out.orientation = section_in.orientation

        for para in doc_in.paragraphs:
            text = para.text
            sentences = split_sentence_by_period(text)
            
            for sentence in sentences:
                if sentence.strip():
                    font_name = None
                    font_size = None
                    start_pos = text.find(sentence)
                    if start_pos != -1 and para.runs:
                        current_pos = 0
                        for run in para.runs:
                            run_text = run.text
                            run_len = len(run_text)
                            if current_pos <= start_pos < current_pos + run_len:
                                font_name = run.font.name if run.font.name else "Georgia"
                                font_size = run.font.size.pt if run.font.size else 10
                                break
                            current_pos += run_len
                    
                    if not font_name:
                        font_name = "Georgia"
                    if not font_size:
                        font_size = 10
                    
                    para_out = doc_out.add_paragraph()
                    run = para_out.add_run(sentence)
                    run.font.name = font_name
                    run.font.size = Pt(font_size)
                    para_out.paragraph_format.space_before = Pt(0)
                    para_out.paragraph_format.space_after = Pt(0)

        output_path = os.path.abspath("processed_output.docx")
        doc_out.save(output_path)

        # 显示完成对话框
        messagebox.showinfo("完成", f"文档处理完成！输出文档为：\n{output_path}")

        # 对话框关闭后，自动更新Word文档A的路径为输出文档路径
        self.word_path.delete(0, tk.END)
        self.word_path.insert(0, output_path)

    def get_col_widths(self, tokens):
        base_width = 0.32
        char_width = 0.21
        max_width = self.max_width_var.get()
        punc_extra = {
            '.': 0.07, ',': 0.07, '?': 0.09, '!': 0.09, ';': 0.07, ':': 0.07, '-': 0.05,
            '"': 0.11, '“': 0.12, '”': 0.12, '’': 0.055, "'": 0.05,
            '(': 0.05, ')': 0.05, '[': 0.05, ']': 0.05, '…': 0.18,
        }
        colwidths = []
        for word in tokens:
            content = re.sub(r"[^A-Za-z0-9]", "", word)
            width = base_width + len(content) * char_width
            if word and not word[-1].isalnum():
                width += punc_extra.get(word[-1], 0)
            width = min(width, max_width)
            colwidths.append(Cm(width))
        return colwidths

    def get_font_name(self, font_type):
        """将中文字体名称映射到 Word 支持的英文名称"""
        font_map = {
            "宋体": "SimSun",
            "黑体": "SimHei",
            "楷体": "KaiTi",
            "微软雅黑": "Microsoft YaHei"
        }
        return font_map.get(font_type, "SimSun")  # 默认使用 SimSun

    def process_doc(self, input_docx, excel_path, font_name, font_size, api_key, set_progress):
        ch_font_name = self.get_font_name(self.fonttype.get())  # 获取映射后的字体名称
        ch_font_size = self.fontsize.get()
        wordset, worddict = load_wordlist(excel_path)
        doc_in = Document(input_docx)
        doc_out = Document()

        # 复制原文档的纸张大小设置
        if doc_in.sections:
            section_in = doc_in.sections[0]
            section_out = doc_out.sections[0]

            # 复制页面尺寸
            section_out.page_width = section_in.page_width
            section_out.page_height = section_in.page_height

            # 复制页边距
            section_out.left_margin = section_in.left_margin
            section_out.right_margin = section_in.right_margin
            section_out.top_margin = section_in.top_margin
            section_out.bottom_margin = section_in.bottom_margin

            # 复制页面方向
            section_out.orientation = section_in.orientation

        # Define a custom style for minimal spacing
        style_name = 'TinySpacing'
        styles = doc_out.styles
        if style_name not in styles:
            style = styles.add_style(style_name, WD_STYLE_TYPE.PARAGRAPH)
            style.base_style = styles['Normal']
            font = style.font
            font.size = Pt(1)
            pf = style.paragraph_format
            pf.space_before = Pt(0)
            pf.space_after = Pt(0)
            pf.line_spacing_rule = WD_LINE_SPACING.EXACTLY
            pf.line_spacing = Pt(1)
            
        all_tasks = set()
        all_para_tokens = []

        for para in doc_in.paragraphs:
            text = para.text
            tokens = extract_tokens(text)
            all_para_tokens.append((tokens, text, para))  # 保存 para 对象以获取字体信息
            ptr = 0
            for token in tokens:
                tkey = strip_punct(token)
                idx = text.lower().find(tkey, ptr) if tkey else -1
                ptr = idx + len(tkey) if idx != -1 else ptr
                if tkey and tkey in wordset and idx != -1:
                    sent = split_sentence_for_context(text, idx).strip()
                    key = (tkey, sent)
                    all_tasks.add(key)

        all_tasks = list(all_tasks)
        set_progress(f"共需AI释义：{len(all_tasks)}", 0)

        def progress_cb(txt, percent=None):
            set_progress(txt, percent)

        meaning_cache = get_chinese_meaning_batch(api_key, all_tasks, progress_cb)
        set_progress("AI查完，排版生成Word...", 100)

        for tokens, text, para in all_para_tokens:
            if not text.strip() or not tokens:
                continue
            colwidths = self.get_col_widths(tokens)
            table = doc_out.add_table(rows=2, cols=len(tokens))
            set_table_noborder_and_tight(table, colwidths)
            table.allow_autofit = False

            ptr = 0
            for j, word in enumerate(tokens):
                cell = table.cell(0, j)
                cell.text = ''
                word_start = text.find(word, ptr)
                font_name = None
                font_size = None
                is_superscript = False
                is_subscript = False

                if word_start != -1 and para.runs:
                    current_pos = 0
                    for run in para.runs:
                        run_text = run.text
                        run_len = len(run_text)
                        if current_pos <= word_start < current_pos + run_len:
                            font_name = run.font.name if run.font.name else "Georgia"
                            font_size = run.font.size.pt if run.font.size else 10
                            is_superscript = run.font.superscript
                            is_subscript = run.font.subscript
                            break
                        current_pos += run_len
                
                if word_start != -1:
                    ptr = word_start + len(word)

                if not font_name:
                    font_name = "Georgia"
                if not font_size:
                    font_size = 10
                
                # 硬编码处理：如果单词是"5"，则添加上标"2"
                if word == "5":
                    run1 = cell.paragraphs[0].add_run("5")
                    run1.font.name = font_name
                    run1.font.size = Pt(font_size)
                    run1.font.superscript = is_superscript
                    run1.font.subscript = is_subscript
                    run1.font.underline = True if strip_punct(word) in wordset else False
                    
                    run2 = cell.paragraphs[0].add_run("2")
                    run2.font.name = font_name
                    run2.font.size = Pt(font_size)
                    run2.font.superscript = True  # 设置为上标
                    run2.font.subscript = False
                    run2.font.underline = False
                else:
                    run = cell.paragraphs[0].add_run(word)
                    run.font.name = font_name
                    run.font.size = Pt(font_size)
                    run.font.superscript = is_superscript
                    run.font.subscript = is_subscript
                    run.font.underline = True if strip_punct(word) in wordset else False
                pf2 = cell.paragraphs[0].paragraph_format
                pf2.space_before = Pt(0)
                pf2.space_after = Pt(0)
                pf2.line_spacing = 1.0
                cell.paragraphs[0].alignment = 1

            ptr = 0
            for j, word in enumerate(tokens):
                cell = table.cell(1, j)
                cell.text = ''
                tkey = strip_punct(word)
                curidx = text.lower().find(tkey, ptr) if tkey else -1
                ptr = curidx + len(tkey) if curidx != -1 else ptr
                sent = split_sentence_for_context(text, curidx).strip() if curidx != -1 else ""
                if tkey in wordset:
                    meaning = meaning_cache.get((tkey, sent), "")
                    logging.info(f"Word: '{tkey}', Sentence: '{sent}', Fetched AI-meaning: '{meaning}'")
                    if meaning.strip():
                        m2 = split_meaning_two_lines(meaning, max_len=8)
                    else:
                        local_meaning = worddict.get(tkey, "（无释义）")
                        logging.info(f"AI meaning is empty, using local dictionary for '{tkey}': '{local_meaning}'")
                        m2 = split_meaning_two_lines(local_meaning, max_len=8)
                    run = cell.paragraphs[0].add_run(m2)
                    run.font.name = ch_font_name
                    r = run._r
                    r.rPr.rFonts.set(qn('w:eastAsia'), ch_font_name)
                    run.font.size = Pt(ch_font_size)
                pf2 = cell.paragraphs[0].paragraph_format
                pf2.space_before = Pt(0)
                pf2.space_after = Pt(0)
                pf2.line_spacing = 1.0
                cell.paragraphs[0].alignment = 1

            for cell in table.rows[0].cells:
                for paragraph in cell.paragraphs:
                    paragraph.paragraph_format.keep_with_next = True

            p = doc_out.add_paragraph()
            if 'TinySpacing' in doc_out.styles:
                p.style = doc_out.styles['TinySpacing']

        output_path = os.path.abspath("output.docx")
        doc_out.save(output_path)
        set_progress("文档已完成。", 100)
        return output_path

if __name__ == "__main__":
    root = tk.Tk()
    app = WordAssistantApp(root)
    root.mainloop()
