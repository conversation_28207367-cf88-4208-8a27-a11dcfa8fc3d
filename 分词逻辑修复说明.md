# 分词逻辑修复说明

## 修复的问题

### 1. 过度合并问题 ✅ 已修复

#### 问题描述
某些不应该合并的字符序列被错误合并了：
- `2to 3equals` 被处理为单个token，但实际上应该分割为 `["2", "to", "3", "equals"]`
- 数字与紧跟的普通英文单词不应该合并

#### 修复方案
**限制数字与字母的合并规则**：

**修复前**：
```python
# 过于宽泛，会合并数字与任何字母
re.match(r'^[a-zA-Z²³¹⁰⁴⁵⁶⁷⁸⁹xX%π°/]', next_token)
```

**修复后**：
```python
# 只合并数字与特定的数学/科学符号
re.match(r'^[xyzXYZ²³¹⁰⁴⁵⁶⁷⁸⁹%π°/]$', next_token)
```

#### 合并规则细化
- ✅ **应该合并**：数字 + 特定符号（x, y, z, X, Y, Z, ², ³, π, %, °等）
- ❌ **不应该合并**：数字 + 普通英文单词（to, equals, and 等）

#### 测试验证
```
输入: "2to 3equals"
修复前: ['2to', '3equals']  ❌
修复后: ['2', 'to', '3', 'equals']  ✅

输入: "5x 10y"
修复前: ['5x', '10', 'y']  ❌
修复后: ['5x', '10y']  ✅
```

### 2. 撇号处理不完整问题 ✅ 已修复

#### 问题描述
某些应该合并的字符序列仍然被分割：
- `film	'  s` 仍然被分割，没有正确合并为 `film's`
- 撇号前后的空格处理逻辑需要优化，包括制表符和多个空格

#### 修复方案
**改进撇号处理的正则表达式**：

**修复前**：
```python
# 只处理普通空格
text = re.sub(r"(\w+)\s+'\s*s\b", r"\1's", text)
text = re.sub(r"(\w+)\s+'\s*([a-zA-Z])", r"\1'\2", text)
```

**修复后**：
```python
# 处理任意空白字符（包括制表符、多个空格）
text = re.sub(r"(\w+)\s*'\s*s\b", r"\1's", text)
text = re.sub(r"(\w+)\s*'\s*([a-zA-Z])", r"\1'\2", text)
```

#### 处理的空白字符类型
- ✅ 普通空格：`film ' s`
- ✅ 制表符：`film\t's`
- ✅ 多个空格：`film  '  s`
- ✅ 混合空白：`film \t' \t s`

#### 测试验证
```
输入: "film\t'  s"
修复前: ['film', '\t', "'", 's']  ❌
修复后: ["film's"]  ✅

输入: "Newton \t' \t s"
修复前: ['Newton', '\t', "'", '\t', 's']  ❌
修复后: ["Newton's"]  ✅
```

## 修复的技术细节

### 1. 预处理阶段改进

#### 数字与符号合并
```python
# 修复前：过于宽泛
text = re.sub(r'(\d+)\s+([xX²³¹⁰⁴⁵⁶⁷⁸⁹%π°])', r'\1\2', text)

# 修复后：包含常用数学变量
text = re.sub(r'(\d+)\s+([xyzXYZ²³¹⁰⁴⁵⁶⁷⁸⁹%π°])', r'\1\2', text)
```

#### 撇号处理改进
```python
# 使用 \s* 匹配任意空白字符（包括制表符）
text = re.sub(r"(\w+)\s*'\s*s\b", r"\1's", text)
text = re.sub(r"(\w+)\s*'\s*([a-zA-Z])", r"\1'\2", text)
text = re.sub(r"(\w+)\s*'\s+", r"\1' ", text)
```

### 2. 智能合并阶段改进

#### 精确的符号匹配
```python
# 修复前：匹配开头字符
re.match(r'^[xyzXYZ²³¹⁰⁴⁵⁶⁷⁸⁹%π°/]', next_token)

# 修复后：精确匹配整个token
re.match(r'^[xyzXYZ²³¹⁰⁴⁵⁶⁷⁸⁹%π°/]$', next_token)
```

## 测试结果

### 过度合并问题测试
- ✅ `2to 3equals` → `['2', 'to', '3', 'equals']`
- ✅ `5x 10y` → `['5x', '10y']`
- ✅ `3π 2² 100%` → `['3π', '2²', '100%']`
- ✅ 复杂句子中数字与单词正确分离

### 撇号处理测试
- ✅ `film\t'  s` → `["film's"]`
- ✅ `Newton \t' \t s` → `["Newton's"]`
- ✅ `revolver ' s` → `["revolver's"]`
- ✅ `children\t\t'\t\ts` → `["children's"]`

### 具体问题案例测试
- ✅ `2to` → `['2', 'to']`
- ✅ `3equals` → `['3', 'equals']`
- ✅ `film\t'  s` → `["film's"]`

## 影响范围

### 正面影响
1. **避免错误合并**：数字与普通英文单词不再被错误合并
2. **改进撇号处理**：支持更复杂的空白字符情况
3. **保持正确合并**：数学变量和符号仍然正确合并
4. **提高准确性**：分词结果更符合语义预期

### 兼容性
- ✅ **向后兼容**：不影响现有的正确分词功能
- ✅ **性能稳定**：修复不会影响处理速度
- ✅ **规则清晰**：合并规则更加明确和可预测

## 使用说明

修复后的分词逻辑会自动应用，无需额外配置。程序现在能够：

1. **智能区分**数字与字母的关系
2. **正确处理**各种空白字符的撇号情况
3. **精确合并**语义相关的字符序列
4. **避免错误**合并不相关的字符

这些修复显著提高了分词的准确性和可靠性，特别是在处理包含数学表达式和复杂标点符号的文档时。
