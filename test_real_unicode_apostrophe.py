#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真正的Unicode撇号字符
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens

def test_real_unicode_apostrophes():
    """测试真正的Unicode撇号字符"""
    print("=== 测试真正的Unicode撇号字符 ===")
    
    # 使用Unicode字符编码创建测试文本
    test_cases = [
        {
            "text": "film" + chr(39) + "s",  # U+0027 标准撇号
            "description": "标准撇号 (U+0027)",
            "unicode_code": 39
        },
        {
            "text": "film" + chr(8216) + "s",  # U+2018 左单引号
            "description": "左单引号 (U+2018)",
            "unicode_code": 8216
        },
        {
            "text": "film" + chr(8217) + "s",  # U+2019 右单引号 - 这是问题字符
            "description": "右单引号 (U+2019) - Word文档常用",
            "unicode_code": 8217
        },
        {
            "text": "film" + chr(96) + "s",  # U+0060 反引号
            "description": "反引号 (U+0060)",
            "unicode_code": 96
        },
        {
            "text": "film" + chr(180) + "s",  # U+00B4 重音符
            "description": "重音符 (U+00B4)",
            "unicode_code": 180
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_case['description']} ---")
        text = test_case['text']
        print(f"输入: '{text}'")
        print(f"字符编码: {[ord(c) for c in text]}")
        print(f"撇号字符: '{text[4]}' (Unicode: {ord(text[4])})")
        
        tokens = extract_tokens(text)
        print(f"分词结果: {tokens}")
        
        # 检查是否正确识别为单个token
        if len(tokens) == 1 and tokens[0] == text:
            print(f"✅ 正确识别为单个token")
        elif any("film" in token and "s" in token for token in tokens):
            # 检查是否有包含film和s的token（可能被转换了）
            apostrophe_token = None
            for token in tokens:
                if "film" in token and "s" in token and len(token) > 5:
                    apostrophe_token = token
                    break
            if apostrophe_token:
                print(f"✅ 被转换并正确识别: '{apostrophe_token}'")
            else:
                print(f"❌ 未正确识别为单个token")
        else:
            print(f"❌ 未正确识别为单个token")
            print(f"   被分割为: {tokens}")

def test_problematic_unicode_sentences():
    """测试包含Unicode撇号的问题句子"""
    print("\n\n=== 测试包含Unicode撇号的问题句子 ===")
    
    # 创建包含真正Unicode撇号的句子
    sentences = [
        {
            "text": "The film" + chr(8217) + "s action scenes were intense.",
            "description": "film's with U+2019",
            "expected_word": "film's"
        },
        {
            "text": "Newton" + chr(8217) + "s third law states that every action has an equal and opposite reaction.",
            "description": "Newton's with U+2019",
            "expected_word": "Newton's"
        }
    ]
    
    for i, sentence_info in enumerate(sentences, 1):
        print(f"\n--- Unicode句子 {i}: {sentence_info['description']} ---")
        text = sentence_info['text']
        print(f"输入: '{text}'")
        
        # 找到并显示Unicode撇号
        for j, char in enumerate(text):
            if ord(char) == 8217:  # U+2019
                print(f"Unicode撇号位置 {j}: '{char}' (U+2019)")
                break
        
        tokens = extract_tokens(text)
        print(f"分词结果: {tokens}")
        print(f"Token数量: {len(tokens)}")
        
        # 检查期望的撇号组合词
        expected_word = sentence_info['expected_word']
        found_apostrophe_words = []
        
        for token in tokens:
            # 检查是否包含撇号字符（任何类型）
            if any(ord(c) in [39, 8216, 8217, 96, 180] for c in token) and len(token) > 2:
                found_apostrophe_words.append(token)
        
        print(f"找到的撇号组合词: {found_apostrophe_words}")
        
        if found_apostrophe_words:
            print(f"✅ 找到撇号组合词，Unicode撇号被正确处理")
            for word in found_apostrophe_words:
                print(f"   '{word}' 将在表格中占据单个单元格")
        else:
            print(f"❌ 未找到撇号组合词，可能被分割")
            
            # 检查是否被分割
            base_words = ["film", "Newton"]
            for base in base_words:
                if base in tokens:
                    print(f"   发现基础词 '{base}' 被分离")
            
            # 检查Unicode撇号是否被单独分离
            for token in tokens:
                if len(token) == 1 and ord(token) == 8217:
                    print(f"   发现Unicode撇号 '{token}' 被单独分离")

def test_mixed_unicode_apostrophes():
    """测试混合Unicode撇号"""
    print("\n\n=== 测试混合Unicode撇号 ===")
    
    # 创建包含不同Unicode撇号的文本
    mixed_text = "The film" + chr(8217) + "s and Newton" + chr(39) + "s laws"
    print(f"混合撇号文本: '{mixed_text}'")
    
    # 显示每个撇号的编码
    apostrophe_info = []
    for i, char in enumerate(mixed_text):
        if ord(char) in [39, 8216, 8217, 96, 180]:
            apostrophe_info.append((i, char, ord(char)))
    
    print(f"撇号信息: {apostrophe_info}")
    
    tokens = extract_tokens(mixed_text)
    print(f"分词结果: {tokens}")
    
    # 统计撇号组合词
    apostrophe_words = []
    for token in tokens:
        if any(ord(c) in [39, 8216, 8217, 96, 180] for c in token) and len(token) > 2:
            apostrophe_words.append(token)
    
    print(f"撇号组合词: {apostrophe_words}")
    print(f"期望数量: 2, 实际数量: {len(apostrophe_words)}")
    
    if len(apostrophe_words) == 2:
        print(f"✅ 所有Unicode撇号组合词都被正确识别")
    else:
        print(f"❌ Unicode撇号组合词识别不完整")

def verify_fix_effectiveness():
    """验证修复的有效性"""
    print("\n\n=== 验证修复有效性 ===")
    
    # 这是导致问题的确切情况
    problematic_text = "The film" + chr(8217) + "s action scenes were intense."
    
    print(f"问题文本: '{problematic_text}'")
    print(f"撇号字符: '{problematic_text[8]}' (Unicode: {ord(problematic_text[8])})")
    
    tokens = extract_tokens(problematic_text)
    print(f"修复后分词结果: {tokens}")
    
    # 检查修复效果
    if len(tokens) == 6:  # 期望的token数量
        apostrophe_words = []
        for token in tokens:
            if any(ord(c) in [39, 8216, 8217, 96, 180] for c in token) and len(token) > 2:
                apostrophe_words.append(token)
        
        if apostrophe_words:
            print(f"✅ 修复成功!")
            print(f"   撇号组合词: {apostrophe_words}")
            print(f"   在表格中将正确显示为单个单元格")
        else:
            print(f"❌ 修复失败: 未找到撇号组合词")
    else:
        print(f"❌ 修复失败: Token数量不正确 (期望6, 实际{len(tokens)})")
        
        # 分析分割情况
        if "film" in tokens and chr(8217) in tokens and "s" in tokens:
            print(f"   Unicode撇号仍被分割为: ['film', '{chr(8217)}', 's']")

if __name__ == "__main__":
    test_real_unicode_apostrophes()
    test_problematic_unicode_sentences()
    test_mixed_unicode_apostrophes()
    verify_fix_effectiveness()
    
    print("\n=== 真正的Unicode撇号测试完成 ===")
    print("\n结论:")
    print("如果测试显示Unicode撇号字符(U+2019)被正确处理，")
    print("那么图片中显示的分割问题已经彻底解决。")
