#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证撇号组合词问题
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens

def test_exact_sentences():
    """测试确切的问题句子"""
    print("=== 验证撇号组合词问题 ===")
    
    test_cases = [
        {
            "sentence": "The film's action scenes were intense.",
            "expected_apostrophe_words": ["film's"],
            "description": "图片中的第一个句子"
        },
        {
            "sentence": "Newton's third law states that every action has an equal and opposite reaction.",
            "expected_apostrophe_words": ["Newton's"],
            "description": "图片中的第二个句子"
        },
        {
            "sentence": "The film ' s action scenes were intense.",
            "expected_apostrophe_words": ["film's"],
            "description": "带空格的film ' s版本"
        },
        {
            "sentence": "Newton ' s third law states that every action has an equal and opposite reaction.",
            "expected_apostrophe_words": ["Newton's"],
            "description": "带空格的Newton ' s版本"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_case['description']} ---")
        print(f"输入句子: '{test_case['sentence']}'")
        
        tokens = extract_tokens(test_case['sentence'])
        print(f"分词结果: {tokens}")
        print(f"Token数量: {len(tokens)}")
        
        # 检查撇号组合词
        found_apostrophe_words = [token for token in tokens if "'" in token and len(token) > 2]
        print(f"找到的撇号组合词: {found_apostrophe_words}")
        
        # 验证期望的撇号组合词
        for expected_word in test_case['expected_apostrophe_words']:
            if expected_word in tokens:
                print(f"✅ 期望的撇号组合词 '{expected_word}' 被正确识别为单个token")
            else:
                print(f"❌ 期望的撇号组合词 '{expected_word}' 未被正确识别")
                
                # 检查是否被分割
                base_word = expected_word.split("'")[0]
                if base_word in tokens:
                    print(f"   发现基础词 '{base_word}' 在结果中")
                    if "'" in tokens:
                        print(f"   发现撇号 \"'\" 被单独分离")
                    if "s" in tokens:
                        print(f"   发现 's' 被单独分离")
                    print(f"   ❌ 撇号组合词被错误分割")
        
        # 检查是否有意外的分割
        if "'" in tokens and len([t for t in tokens if t == "'"]) > 0:
            print(f"⚠️  发现孤立的撇号在结果中")
        
        if "s" in tokens and len([t for t in tokens if t == "s"]) > 0:
            print(f"⚠️  发现孤立的 's' 在结果中")

def test_problematic_patterns():
    """测试可能有问题的模式"""
    print("\n\n=== 测试可能有问题的模式 ===")
    
    # 测试各种撇号字符
    apostrophe_variants = [
        "film's",      # 标准撇号
        "film's",      # 右单引号
        "film's",      # 左单引号
        "film`s",      # 反引号
    ]
    
    for variant in apostrophe_variants:
        print(f"\n测试撇号变体: '{variant}'")
        tokens = extract_tokens(variant)
        print(f"分词结果: {tokens}")
        
        if variant in tokens:
            print(f"✅ 撇号变体被正确识别为单个token")
        else:
            print(f"❌ 撇号变体未被正确识别")

def test_context_sentences():
    """测试在句子上下文中的表现"""
    print("\n\n=== 测试句子上下文中的表现 ===")
    
    context_tests = [
        "The film's action",
        "Newton's third law",
        "children's toys",
        "revolver's trigger",
        "microscope's lens"
    ]
    
    for test_text in context_tests:
        print(f"\n测试: '{test_text}'")
        tokens = extract_tokens(test_text)
        print(f"分词结果: {tokens}")
        
        apostrophe_words = [token for token in tokens if "'" in token and len(token) > 2]
        if apostrophe_words:
            print(f"✅ 撇号组合词: {apostrophe_words}")
        else:
            print(f"❌ 未找到撇号组合词")

def analyze_table_display_issue():
    """分析表格显示问题"""
    print("\n\n=== 分析表格显示问题 ===")
    
    # 如果分词结果是正确的，那么问题可能在表格生成阶段
    test_sentence = "The film's action scenes were intense."
    tokens = extract_tokens(test_sentence)
    
    print(f"句子: '{test_sentence}'")
    print(f"分词结果: {tokens}")
    
    # 模拟表格显示
    print("\n模拟表格显示:")
    print("+" + "-" * (len(tokens) * 12 - 1) + "+")
    
    # 英文行
    english_row = "|"
    for token in tokens:
        english_row += f" {token:^10} |"
    print(english_row)
    
    print("+" + "-" * (len(tokens) * 12 - 1) + "+")
    
    # 中文行（模拟）
    chinese_row = "|"
    for token in tokens:
        if "'" in token:
            chinese_row += f" {'(撇号词)':^10} |"
        elif token.isalpha():
            chinese_row += f" {'(释义)':^10} |"
        else:
            chinese_row += f" {'':^10} |"
    print(chinese_row)
    
    print("+" + "-" * (len(tokens) * 12 - 1) + "+")
    
    print(f"\n分析:")
    print(f"- 如果分词正确，表格应该有 {len(tokens)} 列")
    print(f"- 撇号组合词应该占据单个列")
    
    # 检查是否有撇号组合词
    apostrophe_words = [token for token in tokens if "'" in token and len(token) > 2]
    if apostrophe_words:
        print(f"- 撇号组合词 {apostrophe_words} 应该各占一列")
    else:
        print(f"- 未发现撇号组合词，可能存在分割问题")

if __name__ == "__main__":
    test_exact_sentences()
    test_problematic_patterns()
    test_context_sentences()
    analyze_table_display_issue()
    
    print("\n=== 验证完成 ===")
    print("\n总结:")
    print("如果上述测试显示撇号组合词被正确识别为单个token，")
    print("那么问题可能不在分词阶段，而在表格生成或显示阶段。")
