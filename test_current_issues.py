#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试当前存在的问题
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens

def test_apostrophe_issues():
    """测试撇号组合词分割问题"""
    print("=== 测试撇号组合词分割问题 ===")
    
    test_cases = [
        {
            "input": "The film ' s action scenes were intense.",
            "description": "图片中的film ' s问题",
            "expected_single_token": "film's"
        },
        {
            "input": "film's action",
            "description": "直接的film's（无空格）",
            "expected_single_token": "film's"
        },
        {
            "input": "Newton's third law",
            "description": "Newton's组合词",
            "expected_single_token": "Newton's"
        },
        {
            "input": "children's toys",
            "description": "children's组合词",
            "expected_single_token": "children's"
        },
        {
            "input": "revolver's double",
            "description": "revolver's组合词",
            "expected_single_token": "revolver's"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['description']} ---")
        print(f"输入: '{test_case['input']}'")
        
        tokens = extract_tokens(test_case['input'])
        print(f"分词结果: {tokens}")
        
        # 检查期望的单个token是否存在
        if test_case['expected_single_token'] in tokens:
            print(f"✅ 找到完整的撇号组合词: '{test_case['expected_single_token']}'")
        else:
            print(f"❌ 未找到完整的撇号组合词: '{test_case['expected_single_token']}'")
            
            # 检查是否被错误分割
            base_word = test_case['expected_single_token'].split("'")[0]
            if base_word in tokens:
                print(f"❌ 撇号组合词被分割，找到基础词: '{base_word}'")
                if "'" in tokens:
                    print("❌ 撇号被单独分离")
                if "s" in tokens:
                    print("❌ 's被单独分离")

def test_long_sentence_format():
    """测试长句格式问题"""
    print("\n\n=== 测试长句格式问题 ===")
    
    long_sentences = [
        "Newton's third law states that every action has an equal and opposite reaction.",
        "The revolver's double-action trigger allows both cocking and firing in one motion.",
        "The hydroelectric plant generates enough power for the entire city."
    ]
    
    for i, sentence in enumerate(long_sentences, 1):
        print(f"\n--- 长句测试 {i} ---")
        print(f"输入: '{sentence}'")
        
        tokens = extract_tokens(sentence)
        print(f"分词结果: {tokens}")
        print(f"Token数量: {len(tokens)}")
        
        # 检查是否有撇号组合词被正确处理
        apostrophe_words = [token for token in tokens if "'" in token]
        if apostrophe_words:
            print(f"✅ 撇号组合词: {apostrophe_words}")
        else:
            print("❌ 未找到撇号组合词或被错误分割")

def test_specific_problem_cases():
    """测试图片中的具体问题"""
    print("\n\n=== 测试图片中的具体问题 ===")
    
    # 图片中显示的具体问题
    problem_cases = [
        "The film ' s action scenes were intense.",
        "Newton's third law states that every action has an equal and opposite reaction.",
        "The revolver's double-action trigger allows both cocking and firing in one motion."
    ]
    
    for i, case in enumerate(problem_cases, 1):
        print(f"\n--- 问题案例 {i} ---")
        print(f"输入: '{case}'")
        
        tokens = extract_tokens(case)
        print(f"分词结果: {tokens}")
        
        # 分析问题
        issues = []
        
        # 检查撇号组合词
        if "film" in tokens and "'" in tokens and "s" in tokens:
            issues.append("film's被错误分割")
        elif "film's" in tokens:
            print("✅ film's正确处理")
            
        if "Newton" in tokens and "'" in tokens and "s" in tokens:
            issues.append("Newton's被错误分割")
        elif "Newton's" in tokens:
            print("✅ Newton's正确处理")
            
        if "revolver" in tokens and "'" in tokens and "s" in tokens:
            issues.append("revolver's被错误分割")
        elif "revolver's" in tokens:
            print("✅ revolver's正确处理")
        
        if issues:
            print(f"❌ 发现问题: {', '.join(issues)}")
        
        # 检查长句问题
        if len(tokens) > 10:
            print(f"⚠️  长句包含{len(tokens)}个token，可能需要换行处理")

if __name__ == "__main__":
    test_apostrophe_issues()
    test_long_sentence_format()
    test_specific_problem_cases()
    print("\n=== 问题测试完成 ===")
