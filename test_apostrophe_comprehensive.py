#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试撇号组合词处理
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens

def test_apostrophe_combinations():
    """测试各种撇号组合词"""
    print("=== 测试撇号组合词处理 ===")
    
    test_cases = [
        # 基本撇号组合词
        {
            "input": "film's action scenes were intense.",
            "description": "基本's组合",
            "expected_tokens": ["film's"],
            "should_not_split": ["film", "'", "s"]
        },
        {
            "input": "Newton's third law states that every action has an equal and opposite reaction.",
            "description": "Newton's组合",
            "expected_tokens": ["Newton's"],
            "should_not_split": ["Newton", "'", "s"]
        },
        {
            "input": "The children's toys were scattered everywhere.",
            "description": "children's组合",
            "expected_tokens": ["children's"],
            "should_not_split": ["children", "'", "s"]
        },
        {
            "input": "The revolver's double-action trigger allows both cocking and firing.",
            "description": "revolver's组合",
            "expected_tokens": ["revolver's"],
            "should_not_split": ["revolver", "'", "s"]
        },
        
        # 包含空格的撇号组合词
        {
            "input": "film ' s action",
            "description": "包含空格的film's",
            "expected_tokens": ["film's"],
            "should_not_split": ["film", "'", "s"]
        },
        {
            "input": "Newton ' s law",
            "description": "包含空格的Newton's",
            "expected_tokens": ["Newton's"],
            "should_not_split": ["Newton", "'", "s"]
        },
        
        # 包含制表符和多空格的撇号组合词
        {
            "input": "film\t'  s",
            "description": "制表符+多空格的film's",
            "expected_tokens": ["film's"],
            "should_not_split": ["film", "'", "s"]
        },
        {
            "input": "microscope ' s high power",
            "description": "microscope's组合",
            "expected_tokens": ["microscope's"],
            "should_not_split": ["microscope", "'", "s"]
        },
        
        # 其他常见撇号组合
        {
            "input": "I don't think so.",
            "description": "don't组合",
            "expected_tokens": ["don't"],
            "should_not_split": ["don", "'", "t"]
        },
        {
            "input": "They're coming soon.",
            "description": "They're组合",
            "expected_tokens": ["They're"],
            "should_not_split": ["They", "'", "re"]
        },
        {
            "input": "I'll be there.",
            "description": "I'll组合",
            "expected_tokens": ["I'll"],
            "should_not_split": ["I", "'", "ll"]
        },
        {
            "input": "We've finished the work.",
            "description": "We've组合",
            "expected_tokens": ["We've"],
            "should_not_split": ["We", "'", "ve"]
        },
        
        # 不同撇号字符
        {
            "input": "film's action",  # 使用'
            "description": "使用'字符的film's",
            "expected_tokens": ["film's"],
            "should_not_split": ["film", "'", "s"]
        },
        {
            "input": "film's action",  # 使用'
            "description": "使用'字符的film's",
            "expected_tokens": ["film's"],
            "should_not_split": ["film", "'", "s"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['description']} ---")
        print(f"输入: '{test_case['input']}'")
        
        tokens = extract_tokens(test_case['input'])
        print(f"分词结果: {tokens}")
        
        success = True
        
        # 检查期望的token是否存在
        for expected_token in test_case['expected_tokens']:
            if expected_token in tokens:
                print(f"✅ 找到期望的组合词: '{expected_token}'")
            else:
                print(f"❌ 未找到期望的组合词: '{expected_token}'")
                success = False
        
        # 检查是否错误分割
        if 'should_not_split' in test_case:
            split_found = all(part in tokens for part in test_case['should_not_split'])
            if split_found:
                print(f"❌ 错误分割为: {test_case['should_not_split']}")
                success = False
            else:
                print(f"✅ 正确避免分割: {test_case['should_not_split']}")
        
        if success:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")

def test_specific_problem_cases():
    """测试图片中的具体问题案例"""
    print("\n\n=== 测试具体问题案例 ===")
    
    specific_cases = [
        {
            "input": "The film ' s action scenes were intense.",
            "description": "图片中的film ' s问题",
            "expected": "film's",
            "check_single_token": True
        },
        {
            "input": "The microscope ' s high power lens reveals tiny details.",
            "description": "图片中的microscope ' s问题",
            "expected": "microscope's",
            "check_single_token": True
        },
        {
            "input": "Newton's third law",
            "description": "已经正确的Newton's",
            "expected": "Newton's",
            "check_single_token": True
        }
    ]
    
    for i, test_case in enumerate(specific_cases, 1):
        print(f"\n--- 具体问题 {i}: {test_case['description']} ---")
        print(f"输入: '{test_case['input']}'")
        
        tokens = extract_tokens(test_case['input'])
        print(f"分词结果: {tokens}")
        
        if test_case['expected'] in tokens:
            print(f"✅ 正确处理为单个token: '{test_case['expected']}'")
            
            # 检查是否作为单个token存在
            if test_case.get('check_single_token', False):
                # 确保没有被分割
                base_word = test_case['expected'].split("'")[0]
                if base_word in tokens and "'" in tokens and "s" in tokens:
                    print(f"❌ 仍然被分割为: ['{base_word}', \"'\", 's']")
                else:
                    print(f"✅ 确认作为单个token存在")
        else:
            print(f"❌ 未找到期望的token: '{test_case['expected']}'")

def test_edge_cases():
    """测试边界情况"""
    print("\n\n=== 测试边界情况 ===")
    
    edge_cases = [
        "film's",  # 无空格
        "film 's",  # 单空格
        "film  's",  # 双空格
        "film\t's",  # 制表符
        "film \t 's",  # 混合空白
        "film\n's",  # 换行符
        "film   '   s",  # 多空格
        "film's action scenes",  # 句子中的组合词
        "The film's, Newton's, and children's",  # 多个组合词
    ]
    
    for i, case in enumerate(edge_cases, 1):
        print(f"\n--- 边界测试 {i} ---")
        print(f"输入: '{repr(case)}'")
        
        try:
            tokens = extract_tokens(case)
            print(f"分词结果: {tokens}")
            
            # 检查是否包含完整的撇号组合词
            has_apostrophe_word = any("'" in token and len(token) > 2 for token in tokens)
            if has_apostrophe_word:
                print("✅ 包含完整的撇号组合词")
            else:
                print("❌ 未找到完整的撇号组合词")
                
        except Exception as e:
            print(f"❌ 处理失败: {e}")

if __name__ == "__main__":
    test_apostrophe_combinations()
    test_specific_problem_cases()
    test_edge_cases()
    print("\n=== 撇号组合词测试完成 ===")
