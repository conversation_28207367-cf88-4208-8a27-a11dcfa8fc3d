#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试当前存在的问题
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens, strip_punct

def test_apostrophe_tokenization():
    """测试撇号组合词分词问题"""
    print("=== 测试撇号组合词分词问题 ===")
    
    test_cases = [
        {
            "input": "The film ' s action scenes were intense.",
            "description": "图片中的film ' s问题",
            "expected_single": "film's"
        },
        {
            "input": "Newton ' s third law states that every action has an equal and opposite reaction.",
            "description": "Newton ' s问题",
            "expected_single": "Newton's"
        },
        {
            "input": "She filed a civil action.",
            "description": "普通句子",
            "expected_single": None
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['description']} ---")
        print(f"输入: '{test_case['input']}'")
        
        tokens = extract_tokens(test_case['input'])
        print(f"分词结果: {tokens}")
        
        if test_case['expected_single']:
            if test_case['expected_single'] in tokens:
                print(f"✅ 找到完整的撇号组合词: '{test_case['expected_single']}'")
            else:
                print(f"❌ 未找到完整的撇号组合词: '{test_case['expected_single']}'")
                
                # 检查是否被分割
                base_word = test_case['expected_single'].split("'")[0]
                if base_word in tokens:
                    print(f"❌ 撇号组合词被分割，找到基础词: '{base_word}'")
                    if "'" in tokens:
                        print("❌ 撇号被单独分离")
                    if "s" in tokens:
                        print("❌ 's被单独分离")
        
        # 检查是否有撇号相关的分割问题
        apostrophe_issues = []
        if "'" in tokens:
            apostrophe_issues.append("撇号被单独分离")
        if " s" in tokens or "s " in tokens:
            apostrophe_issues.append("s被错误分离")
        
        if apostrophe_issues:
            print(f"⚠️  发现撇号分割问题: {', '.join(apostrophe_issues)}")

def test_strip_punct_function():
    """测试strip_punct函数对撇号组合词的处理"""
    print("\n\n=== 测试strip_punct函数 ===")
    
    test_words = [
        "film's",
        "Newton's", 
        "children's",
        "action",
        "scenes",
        "film",
        "'",
        "s"
    ]
    
    for word in test_words:
        stripped = strip_punct(word)
        print(f"'{word}' -> '{stripped}'")
        
        # 检查撇号组合词是否被正确处理
        if "'" in word and len(word) > 2:
            if "'" in stripped:
                print(f"  ✅ 撇号组合词保持完整")
            else:
                print(f"  ❌ 撇号被移除，可能影响下划线标记")

def test_underline_logic():
    """测试下划线标记逻辑"""
    print("\n\n=== 测试下划线标记逻辑 ===")
    
    # 模拟词表
    mock_wordset = {"film", "action", "newton", "law", "civil"}
    
    test_words = [
        "film's",
        "Newton's",
        "action",
        "civil",
        "scenes",
        "the"
    ]
    
    for word in test_words:
        stripped = strip_punct(word)
        should_underline = stripped in mock_wordset
        
        print(f"单词: '{word}'")
        print(f"  去标点后: '{stripped}'")
        print(f"  在词表中: {stripped in mock_wordset}")
        print(f"  应该有下划线: {should_underline}")
        
        if "'" in word:
            base_word = word.split("'")[0].lower()
            base_in_wordset = base_word in mock_wordset
            print(f"  基础词: '{base_word}' 在词表中: {base_in_wordset}")
            
            if base_in_wordset and not should_underline:
                print(f"  ❌ 问题：撇号组合词的基础词在词表中，但整体不会被标记下划线")

def test_basic_pattern():
    """测试基本分词模式"""
    print("\n\n=== 测试基本分词模式 ===")
    
    import re
    
    # 当前的基本分词模式
    basic_pattern = r"""
        [A-Za-z]+[''][a-zA-Z]+ |          # 撇号组合词（word's, don't等）
        [A-Za-z]+ |                       # 普通单词
        \d+(?:\.\d+)? |                   # 数字（包括小数）
        [^\w\s]                           # 标点符号和其他字符
    """
    
    test_texts = [
        "The film ' s action",
        "Newton ' s third law",
        "film's action",
        "Newton's law"
    ]
    
    for text in test_texts:
        print(f"\n测试文本: '{text}'")
        tokens = re.findall(basic_pattern, text, re.VERBOSE)
        print(f"基本分词结果: {tokens}")
        
        # 检查撇号组合词是否被正确识别
        apostrophe_words = [token for token in tokens if "'" in token and len(token) > 2]
        if apostrophe_words:
            print(f"  ✅ 识别到撇号组合词: {apostrophe_words}")
        else:
            print(f"  ❌ 未识别到撇号组合词")

if __name__ == "__main__":
    test_apostrophe_tokenization()
    test_strip_punct_function()
    test_underline_logic()
    test_basic_pattern()
    print("\n=== 问题分析完成 ===")
