#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试边界情况和特殊输入
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens

def test_special_input_formats():
    """测试特殊输入格式"""
    print("=== 测试特殊输入格式 ===")
    
    special_cases = [
        # 不同的空格数量
        "The film's action",
        "The film 's action", 
        "The film  's action",
        "The film   's action",
        
        # 不同的撇号字符
        "The film's action",  # 标准撇号
        "The film's action",  # 右单引号
        "The film's action",  # 左单引号
        
        # 制表符和特殊空白
        "The film\t's action",
        "The film\n's action",
        
        # 混合空白字符
        "The film \t ' \t s action",
        "The film  '  s action",
        
        # 大小写变化
        "The FILM'S action",
        "The Film's action",
        
        # 多个撇号组合词
        "The film's and Newton's laws",
        
        # 边界情况
        "'s alone",
        "word' incomplete",
        "multiple ' ' apostrophes",
    ]
    
    for i, test_case in enumerate(special_cases, 1):
        print(f"\n--- 特殊情况 {i} ---")
        print(f"输入: '{repr(test_case)}'")
        
        try:
            tokens = extract_tokens(test_case)
            print(f"分词结果: {tokens}")
            
            # 检查撇号组合词
            apostrophe_words = [token for token in tokens if "'" in token and len(token) > 2]
            if apostrophe_words:
                print(f"✅ 撇号组合词: {apostrophe_words}")
            
            # 检查是否有分割问题
            if "'" in tokens and len([t for t in tokens if t == "'"]) > 0:
                print(f"⚠️  发现孤立撇号")
            if "s" in tokens and len([t for t in tokens if t == "s"]) > 0:
                print(f"⚠️  发现孤立的's'")
                
        except Exception as e:
            print(f"❌ 处理失败: {e}")

def test_document_processing_simulation():
    """模拟文档处理过程"""
    print("\n\n=== 模拟文档处理过程 ===")
    
    # 模拟从Word文档中提取的文本（可能包含特殊格式）
    document_texts = [
        "The film's action scenes were intense.",
        "Newton's third law states that every action has an equal and opposite reaction.",
        # 模拟可能的Word文档格式问题
        "The film\u2019s action scenes were intense.",  # Unicode撇号
        "Newton\u2019s third law states that every action has an equal and opposite reaction.",
    ]
    
    for i, text in enumerate(document_texts, 1):
        print(f"\n--- 文档文本 {i} ---")
        print(f"原始文本: '{text}'")
        print(f"文本编码: {[ord(c) for c in text if not c.isalnum() and not c.isspace()]}")
        
        tokens = extract_tokens(text)
        print(f"分词结果: {tokens}")
        
        # 检查撇号组合词
        apostrophe_words = [token for token in tokens if "'" in token and len(token) > 2]
        if apostrophe_words:
            print(f"✅ 撇号组合词: {apostrophe_words}")
            for word in apostrophe_words:
                print(f"   '{word}' 字符编码: {[ord(c) for c in word]}")
        else:
            print(f"❌ 未找到撇号组合词")

def test_regex_patterns_directly():
    """直接测试正则表达式模式"""
    print("\n\n=== 直接测试正则表达式模式 ===")
    
    import re
    
    # 测试预处理阶段的正则表达式
    apostrophes = r"['\'`''']"
    test_patterns = [
        (rf"(\w+)\s*{apostrophes}\s*s\b", "单词+'s模式"),
        (rf"(\w+)\s*{apostrophes}\s*([a-zA-Z]+)", "单词+撇号+字母模式"),
    ]
    
    test_texts = [
        "film's",
        "film 's",
        "film ' s",
        "film  '  s",
        "Newton's",
        "Newton 's", 
        "Newton ' s",
        "Newton  '  s",
    ]
    
    for pattern, description in test_patterns:
        print(f"\n测试模式: {description}")
        print(f"正则表达式: {pattern}")
        
        for text in test_texts:
            match = re.search(pattern, text)
            if match:
                replacement = rf"\1's" if "s\b" in pattern else rf"\1'\2"
                result = re.sub(pattern, replacement, text)
                print(f"   '{text}' -> 匹配: {match.groups()} -> 替换后: '{result}'")
            else:
                print(f"   '{text}' -> 无匹配")

def test_potential_issues():
    """测试潜在问题"""
    print("\n\n=== 测试潜在问题 ===")
    
    # 测试可能导致问题的情况
    potential_issues = [
        # 连续的撇号
        "film''s action",
        "film'''s action",
        
        # 撇号后面有多个字母
        "film'st action",
        "film'sth action",
        
        # 撇号前后有数字
        "film1's action",
        "film's2 action",
        
        # 特殊标点组合
        "film's, action",
        "film's. action",
        "film's! action",
        
        # 大写字母组合
        "FILM'S ACTION",
        "Film'S Action",
    ]
    
    for i, test_case in enumerate(potential_issues, 1):
        print(f"\n--- 潜在问题 {i} ---")
        print(f"输入: '{test_case}'")
        
        tokens = extract_tokens(test_case)
        print(f"分词结果: {tokens}")
        
        # 分析结果
        apostrophe_tokens = [token for token in tokens if "'" in token]
        if apostrophe_tokens:
            print(f"包含撇号的token: {apostrophe_tokens}")
            
            complete_words = [token for token in apostrophe_tokens if len(token) > 2]
            if complete_words:
                print(f"✅ 完整的撇号组合词: {complete_words}")
            else:
                print(f"❌ 撇号被分割")

if __name__ == "__main__":
    test_special_input_formats()
    test_document_processing_simulation()
    test_regex_patterns_directly()
    test_potential_issues()
    
    print("\n=== 边界情况测试完成 ===")
    print("\n结论:")
    print("如果所有测试都显示撇号组合词被正确处理，")
    print("那么图片中显示的问题可能是:")
    print("1. 旧版本代码的截图")
    print("2. 特定的Word文档格式问题")
    print("3. 表格生成阶段的显示问题")
