#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析字体大小不统一问题
"""

def analyze_font_size_logic():
    """分析字体大小设置逻辑"""
    print("=== 字体大小设置逻辑分析 ===")
    
    print("\n1. 用户界面设置:")
    print("   - 中文字号选择: self.fontsize = tk.IntVar(value=10)")
    print("   - 可选值: 8, 9, 10, 12, 14")
    print("   - 这个设置用于中文释义")
    
    print("\n2. process_doc方法参数:")
    print("   - 接收参数: font_name, font_size (但实际未使用)")
    print("   - 实际使用: ch_font_name, ch_font_size")
    print("   - ch_font_size = self.fontsize.get() (用于中文)")
    
    print("\n3. 英文字体大小设置逻辑:")
    print("   在 _process_table_tokens 方法中:")
    print("   - 尝试从原文档的run中获取字体大小")
    print("   - font_size = run.font.size.pt if run.font.size else 10")
    print("   - 如果获取失败，默认使用10号字体")
    
    print("\n4. 问题分析:")
    print("   ❌ 英文字体大小来源不统一:")
    print("      - 有些来自原文档的run.font.size")
    print("      - 有些使用硬编码的默认值10")
    print("      - 没有使用用户界面的字体大小设置")
    
    print("   ❌ 参数传递问题:")
    print("      - process_doc接收font_size参数但未使用")
    print("      - 英文字体大小没有统一的控制机制")
    
    print("   ❌ 默认值不一致:")
    print("      - 界面默认值: 10 (用于中文)")
    print("      - 代码默认值: 10 (用于英文)")
    print("      - 但两者没有关联")

def propose_solution():
    """提出解决方案"""
    print("\n\n=== 解决方案 ===")
    
    print("\n方案1: 统一使用用户设置的字体大小")
    print("   - 将用户界面的字体大小设置同时应用于英文和中文")
    print("   - 修改_process_table_tokens方法，使用统一的字体大小")
    print("   - 不再依赖原文档的字体大小")
    
    print("\n方案2: 添加独立的英文字体大小设置")
    print("   - 在用户界面添加英文字体大小选择")
    print("   - 分别控制英文和中文的字体大小")
    print("   - 提供更灵活的字体控制")
    
    print("\n推荐方案: 方案1 (统一字体大小)")
    print("   优点:")
    print("   - 简单直接，易于实现")
    print("   - 确保文档字体大小完全统一")
    print("   - 用户界面简洁")
    print("   - 避免字体大小冲突")

def show_current_code_issues():
    """显示当前代码的问题"""
    print("\n\n=== 当前代码问题 ===")
    
    print("\n1. 参数传递问题:")
    print("   process_doc(self, input_docx, excel_path, font_name, font_size, api_key, set_progress)")
    print("   ❌ font_size参数被传入但从未使用")
    
    print("\n2. 字体大小获取逻辑:")
    print("   font_size = run.font.size.pt if run.font.size else 10")
    print("   ❌ 依赖原文档的字体大小，导致不统一")
    
    print("\n3. 默认值硬编码:")
    print("   if not font_size: font_size = 10")
    print("   ❌ 硬编码默认值，没有使用用户设置")
    
    print("\n4. 中英文字体大小分离:")
    print("   - 中文: ch_font_size = self.fontsize.get()")
    print("   - 英文: font_size = run.font.size.pt or 10")
    print("   ❌ 两者没有关联，可能导致大小不一致")

def show_fix_plan():
    """显示修复计划"""
    print("\n\n=== 修复计划 ===")
    
    print("\n步骤1: 修改process_doc方法")
    print("   - 使用用户设置的字体大小作为英文字体大小")
    print("   - 将字体大小传递给_process_table_tokens方法")
    
    print("\n步骤2: 修改_process_table_tokens方法")
    print("   - 接收统一的英文字体大小参数")
    print("   - 不再依赖原文档的字体大小")
    print("   - 使用统一的字体大小设置")
    
    print("\n步骤3: 更新用户界面说明")
    print("   - 将'中文字号'改为'字号'")
    print("   - 说明该设置同时影响英文和中文")
    
    print("\n步骤4: 测试验证")
    print("   - 测试不同字号设置的效果")
    print("   - 验证英文字体大小统一性")
    print("   - 确保中文字体大小正常")

if __name__ == "__main__":
    analyze_font_size_logic()
    propose_solution()
    show_current_code_issues()
    show_fix_plan()
    
    print("\n=== 分析完成 ===")
    print("主要问题: 英文字体大小来源不统一，没有使用用户设置")
    print("解决方案: 统一使用用户界面的字体大小设置")
