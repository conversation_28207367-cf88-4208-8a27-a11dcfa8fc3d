#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Unicode撇号字符修复
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens

def test_unicode_apostrophe_fix():
    """测试Unicode撇号字符修复"""
    print("=== 测试Unicode撇号字符修复 ===")
    
    # 测试不同的撇号字符
    apostrophe_variants = [
        ("film's", "标准撇号 (U+0027)"),
        ("film's", "左单引号 (U+2018)"),
        ("film's", "右单引号 (U+2019) - 这是问题字符"),
        ("film`s", "反引号 (U+0060)"),
        ("film's", "重音符 (U+00B4)"),
    ]
    
    for variant, description in apostrophe_variants:
        print(f"\n--- 测试: {description} ---")
        print(f"输入: '{variant}'")
        print(f"字符编码: {[ord(c) for c in variant]}")
        
        tokens = extract_tokens(variant)
        print(f"分词结果: {tokens}")
        
        # 检查是否正确识别为单个token
        if variant in tokens:
            print(f"✅ 正确识别为单个token")
        else:
            print(f"❌ 未正确识别为单个token")
            
            # 检查是否被分割
            if len(tokens) > 1:
                print(f"   被分割为: {tokens}")

def test_problematic_sentences():
    """测试问题句子"""
    print("\n\n=== 测试问题句子 ===")
    
    # 使用Unicode右单引号的句子（这是Word文档中常见的）
    problem_sentences = [
        "The film's action scenes were intense.",  # U+2019
        "Newton's third law states that every action has an equal and opposite reaction.",  # U+2019
    ]
    
    for i, sentence in enumerate(problem_sentences, 1):
        print(f"\n--- 问题句子 {i} ---")
        print(f"输入: '{sentence}'")
        # 找到撇号字符并显示编码
        apostrophe_char = None
        for char in sentence:
            if char in "'''`":
                apostrophe_char = char
                break
        if apostrophe_char:
            print(f"撇号字符编码: {ord(apostrophe_char)} ({apostrophe_char})")
        
        tokens = extract_tokens(sentence)
        print(f"分词结果: {tokens}")
        
        # 检查撇号组合词
        apostrophe_words = [token for token in tokens if "'" in token or "'" in token or "'" in token]
        if apostrophe_words:
            print(f"✅ 找到撇号组合词: {apostrophe_words}")
        else:
            print(f"❌ 未找到撇号组合词")
            
            # 检查是否被分割
            if "'" in tokens or "'" in tokens or "'" in tokens:
                print(f"   撇号被单独分离")
            if "s" in tokens:
                print(f"   's被单独分离")

def test_mixed_apostrophes():
    """测试混合撇号字符"""
    print("\n\n=== 测试混合撇号字符 ===")
    
    mixed_cases = [
        "The film's and Newton's laws",  # 标准撇号
        "The film's and Newton's laws",  # 混合撇号
        "The film's and Newton's laws",  # 都是Unicode撇号
    ]
    
    for i, case in enumerate(mixed_cases, 1):
        print(f"\n--- 混合测试 {i} ---")
        print(f"输入: '{case}'")
        
        # 显示每个撇号的编码
        apostrophe_positions = []
        for j, char in enumerate(case):
            if char in "'''`":
                apostrophe_positions.append((j, char, ord(char)))
        print(f"撇号位置和编码: {apostrophe_positions}")
        
        tokens = extract_tokens(case)
        print(f"分词结果: {tokens}")
        
        # 统计撇号组合词
        apostrophe_words = []
        for token in tokens:
            if any(apo in token for apo in "'''`") and len(token) > 2:
                apostrophe_words.append(token)
        
        print(f"撇号组合词: {apostrophe_words}")
        print(f"期望数量: 2, 实际数量: {len(apostrophe_words)}")
        
        if len(apostrophe_words) == 2:
            print(f"✅ 所有撇号组合词都被正确识别")
        else:
            print(f"❌ 撇号组合词识别不完整")

def test_word_document_simulation():
    """模拟Word文档中的文本"""
    print("\n\n=== 模拟Word文档文本 ===")
    
    # Word文档通常使用Unicode撇号
    word_doc_texts = [
        "The film's action scenes were intense.",
        "Newton's third law states that every action has an equal and opposite reaction.",
        "The revolver's double-action trigger allows both cocking and firing in one motion.",
        "The microscope's high power lens reveals tiny details.",
    ]
    
    for i, text in enumerate(word_doc_texts, 1):
        print(f"\n--- Word文档文本 {i} ---")
        print(f"输入: '{text}'")
        
        tokens = extract_tokens(text)
        print(f"分词结果: {tokens}")
        print(f"Token数量: {len(tokens)}")
        
        # 检查撇号组合词
        apostrophe_words = []
        for token in tokens:
            if any(apo in token for apo in "'''`") and len(token) > 2:
                apostrophe_words.append(token)
        
        if apostrophe_words:
            print(f"✅ 撇号组合词: {apostrophe_words}")
            for word in apostrophe_words:
                print(f"   '{word}' 应该在表格中占据单个单元格")
        else:
            print(f"❌ 未找到撇号组合词")

def compare_before_after():
    """对比修复前后的效果"""
    print("\n\n=== 对比修复前后效果 ===")
    
    test_text = "The film's action scenes were intense."  # Unicode撇号
    
    print(f"测试文本: '{test_text}'")
    print(f"撇号字符: '{test_text[9]}' (Unicode: {ord(test_text[9])})")
    
    tokens = extract_tokens(test_text)
    print(f"\n修复后的分词结果: {tokens}")
    
    # 检查结果
    if "film's" in tokens:
        print("✅ 修复成功: film's 被识别为单个token")
        print("✅ 在表格中将显示为单个单元格")
    else:
        print("❌ 修复失败: film's 仍被分割")
        if "film" in tokens and "'" in tokens and "s" in tokens:
            print("   被分割为: ['film', ''', 's']")

if __name__ == "__main__":
    test_unicode_apostrophe_fix()
    test_problematic_sentences()
    test_mixed_apostrophes()
    test_word_document_simulation()
    compare_before_after()
    
    print("\n=== Unicode撇号修复测试完成 ===")
    print("\n总结:")
    print("如果测试显示Unicode撇号字符被正确处理，")
    print("那么图片中显示的分割问题应该已经解决。")
