#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的分词逻辑
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens

def test_tokenization():
    """测试分词功能"""
    print("=== 测试改进的分词逻辑 ===")
    
    test_cases = [
        # 数字与字母的组合
        {
            "input": "Calculate 5² using the power rule.",
            "description": "数字与上标的组合",
            "expected_patterns": ["5²"]
        },
        {
            "input": "The microscope has a magnification power of 1000x.",
            "description": "数字与字母x的组合",
            "expected_patterns": ["1000x."]  # 包含句号的完整token
        },
        {
            "input": "The speed is 60 km/h.",
            "description": "单位组合",
            "expected_patterns": ["60km/h."]  # 数字与单位作为一个整体更合理
        },
        
        # 标点符号与相邻字符
        {
            "input": "Stop rating me for every little mistake!\" he shouted angrily.",
            "description": "引号和感叹号的组合",
            "expected_patterns": ["mistake!\""]
        },
        {
            "input": "The film's action scenes were intense.",
            "description": "撇号组合",
            "expected_patterns": ["film's"]
        },
        
        # 数学表达式
        {
            "input": "The equation is x = 3.14π.",
            "description": "数学表达式",
            "expected_patterns": ["=3.14π"]  # 包含等号的完整数学表达式
        },
        
        # 货币符号
        {
            "input": "The price is $25.99 or €20.50.",
            "description": "货币符号组合",
            "expected_patterns": ["$25.99", "€20.50"]
        },
        
        # 复杂组合
        {
            "input": "Newton's third law states that every action has an equal and opposite reaction.",
            "description": "复杂文本",
            "expected_patterns": ["Newton's"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['description']} ---")
        print(f"输入: {test_case['input']}")
        
        tokens = extract_tokens(test_case['input'])
        print(f"分词结果: {tokens}")
        
        # 检查期望的模式是否存在
        success = True
        for expected_pattern in test_case['expected_patterns']:
            if expected_pattern in tokens:
                print(f"✅ 找到期望的模式: '{expected_pattern}'")
            else:
                print(f"❌ 未找到期望的模式: '{expected_pattern}'")
                success = False
        
        if success:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")

def test_specific_cases():
    """测试特定的问题案例"""
    print("\n\n=== 测试特定问题案例 ===")
    
    # 基于图片中的具体问题
    specific_tests = [
        {
            "input": "1000 x",
            "description": "1000x应该合并",
            "check": lambda tokens: "1000x" in tokens or any("1000" in token and "x" in token for token in tokens)
        },
        {
            "input": "film ' s",
            "description": "film's应该合并",
            "check": lambda tokens: "film's" in tokens
        },
        {
            "input": "5²",
            "description": "5²应该保持完整",
            "check": lambda tokens: "5²" in tokens or any("5" in token and "²" in token for token in tokens)
        },
        {
            "input": "mistake!\"",
            "description": "mistake!\"应该合并",
            "check": lambda tokens: any("mistake" in token and "!" in token and "\"" in token for token in tokens)
        }
    ]
    
    for i, test in enumerate(specific_tests, 1):
        print(f"\n--- 特定测试 {i}: {test['description']} ---")
        print(f"输入: '{test['input']}'")
        
        tokens = extract_tokens(test['input'])
        print(f"分词结果: {tokens}")
        
        if test['check'](tokens):
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")

def test_edge_cases():
    """测试边界情况"""
    print("\n\n=== 测试边界情况 ===")
    
    edge_cases = [
        "",  # 空字符串
        "   ",  # 只有空格
        "a",  # 单个字符
        "123",  # 纯数字
        "!@#$%",  # 纯标点
        "word1 word2",  # 简单单词
        "3.14159π²",  # 复杂数学表达式
    ]
    
    for i, case in enumerate(edge_cases, 1):
        print(f"\n--- 边界测试 {i} ---")
        print(f"输入: '{case}'")
        
        try:
            tokens = extract_tokens(case)
            print(f"分词结果: {tokens}")
            print("✅ 处理成功")
        except Exception as e:
            print(f"❌ 处理失败: {e}")

if __name__ == "__main__":
    test_tokenization()
    test_specific_cases()
    test_edge_cases()
    print("\n=== 所有测试完成 ===")
