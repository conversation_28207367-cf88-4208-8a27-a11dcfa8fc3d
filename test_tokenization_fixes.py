#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分词逻辑修复
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens

def test_over_merging_fixes():
    """测试过度合并问题的修复"""
    print("=== 测试过度合并问题修复 ===")
    
    test_cases = [
        {
            "input": "2to 3equals",
            "description": "数字与普通英文单词不应该合并",
            "expected_separate": ["2", "to", "3", "equals"],
            "should_not_merge": ["2to", "3equals"]
        },
        {
            "input": "5x 10y",
            "description": "数字与特定符号应该合并",
            "expected_merged": ["5x", "10y"]
        },
        {
            "input": "3π 2² 100%",
            "description": "数字与数学符号应该合并",
            "expected_merged": ["3π", "2²", "100%"]
        },
        {
            "input": "2to the power of 3equals 8",
            "description": "复杂句子中的数字与单词分离",
            "expected_separate": ["2", "to", "3", "equals"],
            "should_not_merge": ["2to", "3equals"]
        },
        {
            "input": "In math, 2to the power of 3equals 8.",
            "description": "句子中数字与普通单词不合并",
            "expected_separate": ["2", "to", "3", "equals"],
            "should_not_merge": ["2to", "3equals"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['description']} ---")
        print(f"输入: '{test_case['input']}'")
        
        tokens = extract_tokens(test_case['input'])
        print(f"分词结果: {tokens}")
        
        success = True
        
        # 检查不应该合并的情况
        if 'should_not_merge' in test_case:
            for bad_merge in test_case['should_not_merge']:
                if bad_merge in tokens:
                    print(f"❌ 错误合并了: '{bad_merge}'")
                    success = False
                else:
                    print(f"✅ 正确避免合并: '{bad_merge}'")
        
        # 检查应该分离的token
        if 'expected_separate' in test_case:
            for expected_token in test_case['expected_separate']:
                if expected_token in tokens:
                    print(f"✅ 正确分离: '{expected_token}'")
                else:
                    print(f"❌ 未找到分离的token: '{expected_token}'")
                    success = False
        
        # 检查应该合并的token
        if 'expected_merged' in test_case:
            for expected_token in test_case['expected_merged']:
                if expected_token in tokens:
                    print(f"✅ 正确合并: '{expected_token}'")
                else:
                    print(f"❌ 未找到合并的token: '{expected_token}'")
                    success = False
        
        if success:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")

def test_apostrophe_fixes():
    """测试撇号处理问题的修复"""
    print("\n\n=== 测试撇号处理修复 ===")
    
    test_cases = [
        {
            "input": "film\t'  s",
            "description": "包含制表符和多个空格的撇号",
            "expected": "film's"
        },
        {
            "input": "Newton \t' \t s",
            "description": "复杂空格情况的撇号",
            "expected": "Newton's"
        },
        {
            "input": "The film ' s action",
            "description": "普通空格的撇号",
            "expected": "film's"
        },
        {
            "input": "revolver ' s double",
            "description": "单个空格的撇号",
            "expected": "revolver's"
        },
        {
            "input": "children\t\t'\t\ts",
            "description": "多个制表符的撇号",
            "expected": "children's"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 撇号测试 {i}: {test_case['description']} ---")
        print(f"输入: '{repr(test_case['input'])}'")  # 使用repr显示特殊字符
        
        tokens = extract_tokens(test_case['input'])
        print(f"分词结果: {tokens}")
        
        if test_case['expected'] in tokens:
            print(f"✅ 正确处理撇号: '{test_case['expected']}'")
        else:
            print(f"❌ 撇号处理失败，期望: '{test_case['expected']}'")

def test_specific_problem_cases():
    """测试图片中的具体问题案例"""
    print("\n\n=== 测试具体问题案例 ===")
    
    specific_cases = [
        {
            "input": "2to",
            "description": "2to应该分离为2和to",
            "expected_tokens": ["2", "to"]
        },
        {
            "input": "3equals",
            "description": "3equals应该分离为3和equals",
            "expected_tokens": ["3", "equals"]
        },
        {
            "input": "film\t'  s",
            "description": "film加制表符撇号空格s应该合并",
            "expected_tokens": ["film's"]
        }
    ]
    
    for i, test_case in enumerate(specific_cases, 1):
        print(f"\n--- 具体问题 {i}: {test_case['description']} ---")
        print(f"输入: '{repr(test_case['input'])}'")
        
        tokens = extract_tokens(test_case['input'])
        print(f"分词结果: {tokens}")
        
        success = True
        for expected_token in test_case['expected_tokens']:
            if expected_token in tokens:
                print(f"✅ 找到期望token: '{expected_token}'")
            else:
                print(f"❌ 未找到期望token: '{expected_token}'")
                success = False
        
        if success:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")

if __name__ == "__main__":
    test_over_merging_fixes()
    test_apostrophe_fixes()
    test_specific_problem_cases()
    print("\n=== 所有修复测试完成 ===")
