#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复效果
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oringin import extract_tokens, strip_punct

def test_apostrophe_fixes():
    """测试撇号组合词修复效果"""
    print("=== 测试撇号组合词修复效果 ===")
    
    test_cases = [
        {
            "input": "The film ' s action scenes were intense.",
            "description": "图片中的film ' s问题",
            "expected_single": "film's"
        },
        {
            "input": "Newton ' s third law states that every action has an equal and opposite reaction.",
            "description": "Newton ' s问题",
            "expected_single": "Newton's"
        },
        {
            "input": "film's action",
            "description": "直接的film's（无空格）",
            "expected_single": "film's"
        },
        {
            "input": "Newton's law",
            "description": "直接的Newton's（无空格）",
            "expected_single": "Newton's"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['description']} ---")
        print(f"输入: '{test_case['input']}'")
        
        tokens = extract_tokens(test_case['input'])
        print(f"分词结果: {tokens}")
        
        if test_case['expected_single'] in tokens:
            print(f"✅ 找到完整的撇号组合词: '{test_case['expected_single']}'")
        else:
            print(f"❌ 未找到完整的撇号组合词: '{test_case['expected_single']}'")
            
            # 检查是否被分割
            base_word = test_case['expected_single'].split("'")[0]
            if base_word in tokens and "'" in tokens and "s" in tokens:
                print(f"❌ 撇号组合词被分割为: ['{base_word}', \"'\", 's']")

def test_underline_fixes():
    """测试下划线标记修复效果"""
    print("\n\n=== 测试下划线标记修复效果 ===")
    
    # 模拟词表
    mock_wordset = {"film", "action", "newton", "law", "civil", "scenes"}
    
    test_words = [
        "film's",
        "Newton's", 
        "children's",
        "action",
        "civil",
        "scenes",
        "the"
    ]
    
    for word in test_words:
        stripped = strip_punct(word)
        should_underline = stripped in mock_wordset
        
        print(f"\n单词: '{word}'")
        print(f"  去标点后: '{stripped}'")
        print(f"  在词表中: {stripped in mock_wordset}")
        print(f"  应该有下划线: {should_underline}")
        
        if "'" in word and len(word) > 2:
            base_word = word.split("'")[0].lower()
            base_in_wordset = base_word in mock_wordset
            print(f"  基础词: '{base_word}' 在词表中: {base_in_wordset}")
            
            if base_in_wordset and should_underline:
                print(f"  ✅ 修复成功：撇号组合词的基础词在词表中，整体会被标记下划线")
            elif base_in_wordset and not should_underline:
                print(f"  ❌ 仍有问题：撇号组合词的基础词在词表中，但整体不会被标记下划线")

def test_complete_workflow():
    """测试完整的工作流程"""
    print("\n\n=== 测试完整工作流程 ===")
    
    test_sentences = [
        "The film ' s action scenes were intense.",
        "Newton ' s third law states that every action has an equal and opposite reaction.",
        "She filed a civil action."
    ]
    
    # 模拟词表
    mock_wordset = {"film", "action", "newton", "law", "civil", "scenes", "filed", "she"}
    
    for i, sentence in enumerate(test_sentences, 1):
        print(f"\n--- 句子 {i}: {sentence} ---")
        
        tokens = extract_tokens(sentence)
        print(f"分词结果: {tokens}")
        
        # 检查撇号组合词
        apostrophe_words = [token for token in tokens if "'" in token and len(token) > 2]
        if apostrophe_words:
            print(f"撇号组合词: {apostrophe_words}")
        
        # 检查下划线标记
        underlined_words = []
        for token in tokens:
            stripped = strip_punct(token)
            if stripped in mock_wordset:
                underlined_words.append(token)
        
        print(f"应该有下划线的单词: {underlined_words}")
        
        # 验证撇号组合词的下划线
        for apostrophe_word in apostrophe_words:
            stripped = strip_punct(apostrophe_word)
            if stripped in mock_wordset:
                print(f"✅ 撇号组合词 '{apostrophe_word}' 会有下划线（基础词: '{stripped}'）")
            else:
                print(f"❌ 撇号组合词 '{apostrophe_word}' 不会有下划线（基础词: '{stripped}' 不在词表中）")

if __name__ == "__main__":
    test_apostrophe_fixes()
    test_underline_fixes()
    test_complete_workflow()
    print("\n=== 修复效果测试完成 ===")
